/* Autogenerated by tools/make_headers_inc.py */

struct cmark_entity_node {
	unsigned char *entity;
        unsigned char bytes[8];
};

#define CMARK_ENTITY_MIN_LENGTH 2
#define CMARK_ENTITY_MAX_LENGTH 32
#define CMARK_NUM_ENTITIES 2125

static const struct cmark_entity_node cmark_entities[] = {
{(unsigned char*)"AElig", {195, 134, 0}},
{(unsigned char*)"AMP", {38, 0}},
{(unsigned char*)"Aacute", {195, 129, 0}},
{(unsigned char*)"Abreve", {196, 130, 0}},
{(unsigned char*)"Acirc", {195, 130, 0}},
{(unsigned char*)"Acy", {208, 144, 0}},
{(unsigned char*)"Afr", {240, 157, 148, 132, 0}},
{(unsigned char*)"Agrave", {195, 128, 0}},
{(unsigned char*)"Alpha", {206, 145, 0}},
{(unsigned char*)"Amacr", {196, 128, 0}},
{(unsigned char*)"And", {226, 169, 147, 0}},
{(unsigned char*)"Aogon", {196, 132, 0}},
{(unsigned char*)"Aopf", {240, 157, 148, 184, 0}},
{(unsigned char*)"ApplyFunction", {226, 129, 161, 0}},
{(unsigned char*)"Aring", {195, 133, 0}},
{(unsigned char*)"Ascr", {240, 157, 146, 156, 0}},
{(unsigned char*)"Assign", {226, 137, 148, 0}},
{(unsigned char*)"Atilde", {195, 131, 0}},
{(unsigned char*)"Auml", {195, 132, 0}},
{(unsigned char*)"Backslash", {226, 136, 150, 0}},
{(unsigned char*)"Barv", {226, 171, 167, 0}},
{(unsigned char*)"Barwed", {226, 140, 134, 0}},
{(unsigned char*)"Bcy", {208, 145, 0}},
{(unsigned char*)"Because", {226, 136, 181, 0}},
{(unsigned char*)"Bernoullis", {226, 132, 172, 0}},
{(unsigned char*)"Beta", {206, 146, 0}},
{(unsigned char*)"Bfr", {240, 157, 148, 133, 0}},
{(unsigned char*)"Bopf", {240, 157, 148, 185, 0}},
{(unsigned char*)"Breve", {203, 152, 0}},
{(unsigned char*)"Bscr", {226, 132, 172, 0}},
{(unsigned char*)"Bumpeq", {226, 137, 142, 0}},
{(unsigned char*)"CHcy", {208, 167, 0}},
{(unsigned char*)"COPY", {194, 169, 0}},
{(unsigned char*)"Cacute", {196, 134, 0}},
{(unsigned char*)"Cap", {226, 139, 146, 0}},
{(unsigned char*)"CapitalDifferentialD", {226, 133, 133, 0}},
{(unsigned char*)"Cayleys", {226, 132, 173, 0}},
{(unsigned char*)"Ccaron", {196, 140, 0}},
{(unsigned char*)"Ccedil", {195, 135, 0}},
{(unsigned char*)"Ccirc", {196, 136, 0}},
{(unsigned char*)"Cconint", {226, 136, 176, 0}},
{(unsigned char*)"Cdot", {196, 138, 0}},
{(unsigned char*)"Cedilla", {194, 184, 0}},
{(unsigned char*)"CenterDot", {194, 183, 0}},
{(unsigned char*)"Cfr", {226, 132, 173, 0}},
{(unsigned char*)"Chi", {206, 167, 0}},
{(unsigned char*)"CircleDot", {226, 138, 153, 0}},
{(unsigned char*)"CircleMinus", {226, 138, 150, 0}},
{(unsigned char*)"CirclePlus", {226, 138, 149, 0}},
{(unsigned char*)"CircleTimes", {226, 138, 151, 0}},
{(unsigned char*)"ClockwiseContourIntegral", {226, 136, 178, 0}},
{(unsigned char*)"CloseCurlyDoubleQuote", {226, 128, 157, 0}},
{(unsigned char*)"CloseCurlyQuote", {226, 128, 153, 0}},
{(unsigned char*)"Colon", {226, 136, 183, 0}},
{(unsigned char*)"Colone", {226, 169, 180, 0}},
{(unsigned char*)"Congruent", {226, 137, 161, 0}},
{(unsigned char*)"Conint", {226, 136, 175, 0}},
{(unsigned char*)"ContourIntegral", {226, 136, 174, 0}},
{(unsigned char*)"Copf", {226, 132, 130, 0}},
{(unsigned char*)"Coproduct", {226, 136, 144, 0}},
{(unsigned char*)"CounterClockwiseContourIntegral", {226, 136, 179, 0}},
{(unsigned char*)"Cross", {226, 168, 175, 0}},
{(unsigned char*)"Cscr", {240, 157, 146, 158, 0}},
{(unsigned char*)"Cup", {226, 139, 147, 0}},
{(unsigned char*)"CupCap", {226, 137, 141, 0}},
{(unsigned char*)"DD", {226, 133, 133, 0}},
{(unsigned char*)"DDotrahd", {226, 164, 145, 0}},
{(unsigned char*)"DJcy", {208, 130, 0}},
{(unsigned char*)"DScy", {208, 133, 0}},
{(unsigned char*)"DZcy", {208, 143, 0}},
{(unsigned char*)"Dagger", {226, 128, 161, 0}},
{(unsigned char*)"Darr", {226, 134, 161, 0}},
{(unsigned char*)"Dashv", {226, 171, 164, 0}},
{(unsigned char*)"Dcaron", {196, 142, 0}},
{(unsigned char*)"Dcy", {208, 148, 0}},
{(unsigned char*)"Del", {226, 136, 135, 0}},
{(unsigned char*)"Delta", {206, 148, 0}},
{(unsigned char*)"Dfr", {240, 157, 148, 135, 0}},
{(unsigned char*)"DiacriticalAcute", {194, 180, 0}},
{(unsigned char*)"DiacriticalDot", {203, 153, 0}},
{(unsigned char*)"DiacriticalDoubleAcute", {203, 157, 0}},
{(unsigned char*)"DiacriticalGrave", {96, 0}},
{(unsigned char*)"DiacriticalTilde", {203, 156, 0}},
{(unsigned char*)"Diamond", {226, 139, 132, 0}},
{(unsigned char*)"DifferentialD", {226, 133, 134, 0}},
{(unsigned char*)"Dopf", {240, 157, 148, 187, 0}},
{(unsigned char*)"Dot", {194, 168, 0}},
{(unsigned char*)"DotDot", {226, 131, 156, 0}},
{(unsigned char*)"DotEqual", {226, 137, 144, 0}},
{(unsigned char*)"DoubleContourIntegral", {226, 136, 175, 0}},
{(unsigned char*)"DoubleDot", {194, 168, 0}},
{(unsigned char*)"DoubleDownArrow", {226, 135, 147, 0}},
{(unsigned char*)"DoubleLeftArrow", {226, 135, 144, 0}},
{(unsigned char*)"DoubleLeftRightArrow", {226, 135, 148, 0}},
{(unsigned char*)"DoubleLeftTee", {226, 171, 164, 0}},
{(unsigned char*)"DoubleLongLeftArrow", {226, 159, 184, 0}},
{(unsigned char*)"DoubleLongLeftRightArrow", {226, 159, 186, 0}},
{(unsigned char*)"DoubleLongRightArrow", {226, 159, 185, 0}},
{(unsigned char*)"DoubleRightArrow", {226, 135, 146, 0}},
{(unsigned char*)"DoubleRightTee", {226, 138, 168, 0}},
{(unsigned char*)"DoubleUpArrow", {226, 135, 145, 0}},
{(unsigned char*)"DoubleUpDownArrow", {226, 135, 149, 0}},
{(unsigned char*)"DoubleVerticalBar", {226, 136, 165, 0}},
{(unsigned char*)"DownArrow", {226, 134, 147, 0}},
{(unsigned char*)"DownArrowBar", {226, 164, 147, 0}},
{(unsigned char*)"DownArrowUpArrow", {226, 135, 181, 0}},
{(unsigned char*)"DownBreve", {204, 145, 0}},
{(unsigned char*)"DownLeftRightVector", {226, 165, 144, 0}},
{(unsigned char*)"DownLeftTeeVector", {226, 165, 158, 0}},
{(unsigned char*)"DownLeftVector", {226, 134, 189, 0}},
{(unsigned char*)"DownLeftVectorBar", {226, 165, 150, 0}},
{(unsigned char*)"DownRightTeeVector", {226, 165, 159, 0}},
{(unsigned char*)"DownRightVector", {226, 135, 129, 0}},
{(unsigned char*)"DownRightVectorBar", {226, 165, 151, 0}},
{(unsigned char*)"DownTee", {226, 138, 164, 0}},
{(unsigned char*)"DownTeeArrow", {226, 134, 167, 0}},
{(unsigned char*)"Downarrow", {226, 135, 147, 0}},
{(unsigned char*)"Dscr", {240, 157, 146, 159, 0}},
{(unsigned char*)"Dstrok", {196, 144, 0}},
{(unsigned char*)"ENG", {197, 138, 0}},
{(unsigned char*)"ETH", {195, 144, 0}},
{(unsigned char*)"Eacute", {195, 137, 0}},
{(unsigned char*)"Ecaron", {196, 154, 0}},
{(unsigned char*)"Ecirc", {195, 138, 0}},
{(unsigned char*)"Ecy", {208, 173, 0}},
{(unsigned char*)"Edot", {196, 150, 0}},
{(unsigned char*)"Efr", {240, 157, 148, 136, 0}},
{(unsigned char*)"Egrave", {195, 136, 0}},
{(unsigned char*)"Element", {226, 136, 136, 0}},
{(unsigned char*)"Emacr", {196, 146, 0}},
{(unsigned char*)"EmptySmallSquare", {226, 151, 187, 0}},
{(unsigned char*)"EmptyVerySmallSquare", {226, 150, 171, 0}},
{(unsigned char*)"Eogon", {196, 152, 0}},
{(unsigned char*)"Eopf", {240, 157, 148, 188, 0}},
{(unsigned char*)"Epsilon", {206, 149, 0}},
{(unsigned char*)"Equal", {226, 169, 181, 0}},
{(unsigned char*)"EqualTilde", {226, 137, 130, 0}},
{(unsigned char*)"Equilibrium", {226, 135, 140, 0}},
{(unsigned char*)"Escr", {226, 132, 176, 0}},
{(unsigned char*)"Esim", {226, 169, 179, 0}},
{(unsigned char*)"Eta", {206, 151, 0}},
{(unsigned char*)"Euml", {195, 139, 0}},
{(unsigned char*)"Exists", {226, 136, 131, 0}},
{(unsigned char*)"ExponentialE", {226, 133, 135, 0}},
{(unsigned char*)"Fcy", {208, 164, 0}},
{(unsigned char*)"Ffr", {240, 157, 148, 137, 0}},
{(unsigned char*)"FilledSmallSquare", {226, 151, 188, 0}},
{(unsigned char*)"FilledVerySmallSquare", {226, 150, 170, 0}},
{(unsigned char*)"Fopf", {240, 157, 148, 189, 0}},
{(unsigned char*)"ForAll", {226, 136, 128, 0}},
{(unsigned char*)"Fouriertrf", {226, 132, 177, 0}},
{(unsigned char*)"Fscr", {226, 132, 177, 0}},
{(unsigned char*)"GJcy", {208, 131, 0}},
{(unsigned char*)"GT", {62, 0}},
{(unsigned char*)"Gamma", {206, 147, 0}},
{(unsigned char*)"Gammad", {207, 156, 0}},
{(unsigned char*)"Gbreve", {196, 158, 0}},
{(unsigned char*)"Gcedil", {196, 162, 0}},
{(unsigned char*)"Gcirc", {196, 156, 0}},
{(unsigned char*)"Gcy", {208, 147, 0}},
{(unsigned char*)"Gdot", {196, 160, 0}},
{(unsigned char*)"Gfr", {240, 157, 148, 138, 0}},
{(unsigned char*)"Gg", {226, 139, 153, 0}},
{(unsigned char*)"Gopf", {240, 157, 148, 190, 0}},
{(unsigned char*)"GreaterEqual", {226, 137, 165, 0}},
{(unsigned char*)"GreaterEqualLess", {226, 139, 155, 0}},
{(unsigned char*)"GreaterFullEqual", {226, 137, 167, 0}},
{(unsigned char*)"GreaterGreater", {226, 170, 162, 0}},
{(unsigned char*)"GreaterLess", {226, 137, 183, 0}},
{(unsigned char*)"GreaterSlantEqual", {226, 169, 190, 0}},
{(unsigned char*)"GreaterTilde", {226, 137, 179, 0}},
{(unsigned char*)"Gscr", {240, 157, 146, 162, 0}},
{(unsigned char*)"Gt", {226, 137, 171, 0}},
{(unsigned char*)"HARDcy", {208, 170, 0}},
{(unsigned char*)"Hacek", {203, 135, 0}},
{(unsigned char*)"Hat", {94, 0}},
{(unsigned char*)"Hcirc", {196, 164, 0}},
{(unsigned char*)"Hfr", {226, 132, 140, 0}},
{(unsigned char*)"HilbertSpace", {226, 132, 139, 0}},
{(unsigned char*)"Hopf", {226, 132, 141, 0}},
{(unsigned char*)"HorizontalLine", {226, 148, 128, 0}},
{(unsigned char*)"Hscr", {226, 132, 139, 0}},
{(unsigned char*)"Hstrok", {196, 166, 0}},
{(unsigned char*)"HumpDownHump", {226, 137, 142, 0}},
{(unsigned char*)"HumpEqual", {226, 137, 143, 0}},
{(unsigned char*)"IEcy", {208, 149, 0}},
{(unsigned char*)"IJlig", {196, 178, 0}},
{(unsigned char*)"IOcy", {208, 129, 0}},
{(unsigned char*)"Iacute", {195, 141, 0}},
{(unsigned char*)"Icirc", {195, 142, 0}},
{(unsigned char*)"Icy", {208, 152, 0}},
{(unsigned char*)"Idot", {196, 176, 0}},
{(unsigned char*)"Ifr", {226, 132, 145, 0}},
{(unsigned char*)"Igrave", {195, 140, 0}},
{(unsigned char*)"Im", {226, 132, 145, 0}},
{(unsigned char*)"Imacr", {196, 170, 0}},
{(unsigned char*)"ImaginaryI", {226, 133, 136, 0}},
{(unsigned char*)"Implies", {226, 135, 146, 0}},
{(unsigned char*)"Int", {226, 136, 172, 0}},
{(unsigned char*)"Integral", {226, 136, 171, 0}},
{(unsigned char*)"Intersection", {226, 139, 130, 0}},
{(unsigned char*)"InvisibleComma", {226, 129, 163, 0}},
{(unsigned char*)"InvisibleTimes", {226, 129, 162, 0}},
{(unsigned char*)"Iogon", {196, 174, 0}},
{(unsigned char*)"Iopf", {240, 157, 149, 128, 0}},
{(unsigned char*)"Iota", {206, 153, 0}},
{(unsigned char*)"Iscr", {226, 132, 144, 0}},
{(unsigned char*)"Itilde", {196, 168, 0}},
{(unsigned char*)"Iukcy", {208, 134, 0}},
{(unsigned char*)"Iuml", {195, 143, 0}},
{(unsigned char*)"Jcirc", {196, 180, 0}},
{(unsigned char*)"Jcy", {208, 153, 0}},
{(unsigned char*)"Jfr", {240, 157, 148, 141, 0}},
{(unsigned char*)"Jopf", {240, 157, 149, 129, 0}},
{(unsigned char*)"Jscr", {240, 157, 146, 165, 0}},
{(unsigned char*)"Jsercy", {208, 136, 0}},
{(unsigned char*)"Jukcy", {208, 132, 0}},
{(unsigned char*)"KHcy", {208, 165, 0}},
{(unsigned char*)"KJcy", {208, 140, 0}},
{(unsigned char*)"Kappa", {206, 154, 0}},
{(unsigned char*)"Kcedil", {196, 182, 0}},
{(unsigned char*)"Kcy", {208, 154, 0}},
{(unsigned char*)"Kfr", {240, 157, 148, 142, 0}},
{(unsigned char*)"Kopf", {240, 157, 149, 130, 0}},
{(unsigned char*)"Kscr", {240, 157, 146, 166, 0}},
{(unsigned char*)"LJcy", {208, 137, 0}},
{(unsigned char*)"LT", {60, 0}},
{(unsigned char*)"Lacute", {196, 185, 0}},
{(unsigned char*)"Lambda", {206, 155, 0}},
{(unsigned char*)"Lang", {226, 159, 170, 0}},
{(unsigned char*)"Laplacetrf", {226, 132, 146, 0}},
{(unsigned char*)"Larr", {226, 134, 158, 0}},
{(unsigned char*)"Lcaron", {196, 189, 0}},
{(unsigned char*)"Lcedil", {196, 187, 0}},
{(unsigned char*)"Lcy", {208, 155, 0}},
{(unsigned char*)"LeftAngleBracket", {226, 159, 168, 0}},
{(unsigned char*)"LeftArrow", {226, 134, 144, 0}},
{(unsigned char*)"LeftArrowBar", {226, 135, 164, 0}},
{(unsigned char*)"LeftArrowRightArrow", {226, 135, 134, 0}},
{(unsigned char*)"LeftCeiling", {226, 140, 136, 0}},
{(unsigned char*)"LeftDoubleBracket", {226, 159, 166, 0}},
{(unsigned char*)"LeftDownTeeVector", {226, 165, 161, 0}},
{(unsigned char*)"LeftDownVector", {226, 135, 131, 0}},
{(unsigned char*)"LeftDownVectorBar", {226, 165, 153, 0}},
{(unsigned char*)"LeftFloor", {226, 140, 138, 0}},
{(unsigned char*)"LeftRightArrow", {226, 134, 148, 0}},
{(unsigned char*)"LeftRightVector", {226, 165, 142, 0}},
{(unsigned char*)"LeftTee", {226, 138, 163, 0}},
{(unsigned char*)"LeftTeeArrow", {226, 134, 164, 0}},
{(unsigned char*)"LeftTeeVector", {226, 165, 154, 0}},
{(unsigned char*)"LeftTriangle", {226, 138, 178, 0}},
{(unsigned char*)"LeftTriangleBar", {226, 167, 143, 0}},
{(unsigned char*)"LeftTriangleEqual", {226, 138, 180, 0}},
{(unsigned char*)"LeftUpDownVector", {226, 165, 145, 0}},
{(unsigned char*)"LeftUpTeeVector", {226, 165, 160, 0}},
{(unsigned char*)"LeftUpVector", {226, 134, 191, 0}},
{(unsigned char*)"LeftUpVectorBar", {226, 165, 152, 0}},
{(unsigned char*)"LeftVector", {226, 134, 188, 0}},
{(unsigned char*)"LeftVectorBar", {226, 165, 146, 0}},
{(unsigned char*)"Leftarrow", {226, 135, 144, 0}},
{(unsigned char*)"Leftrightarrow", {226, 135, 148, 0}},
{(unsigned char*)"LessEqualGreater", {226, 139, 154, 0}},
{(unsigned char*)"LessFullEqual", {226, 137, 166, 0}},
{(unsigned char*)"LessGreater", {226, 137, 182, 0}},
{(unsigned char*)"LessLess", {226, 170, 161, 0}},
{(unsigned char*)"LessSlantEqual", {226, 169, 189, 0}},
{(unsigned char*)"LessTilde", {226, 137, 178, 0}},
{(unsigned char*)"Lfr", {240, 157, 148, 143, 0}},
{(unsigned char*)"Ll", {226, 139, 152, 0}},
{(unsigned char*)"Lleftarrow", {226, 135, 154, 0}},
{(unsigned char*)"Lmidot", {196, 191, 0}},
{(unsigned char*)"LongLeftArrow", {226, 159, 181, 0}},
{(unsigned char*)"LongLeftRightArrow", {226, 159, 183, 0}},
{(unsigned char*)"LongRightArrow", {226, 159, 182, 0}},
{(unsigned char*)"Longleftarrow", {226, 159, 184, 0}},
{(unsigned char*)"Longleftrightarrow", {226, 159, 186, 0}},
{(unsigned char*)"Longrightarrow", {226, 159, 185, 0}},
{(unsigned char*)"Lopf", {240, 157, 149, 131, 0}},
{(unsigned char*)"LowerLeftArrow", {226, 134, 153, 0}},
{(unsigned char*)"LowerRightArrow", {226, 134, 152, 0}},
{(unsigned char*)"Lscr", {226, 132, 146, 0}},
{(unsigned char*)"Lsh", {226, 134, 176, 0}},
{(unsigned char*)"Lstrok", {197, 129, 0}},
{(unsigned char*)"Lt", {226, 137, 170, 0}},
{(unsigned char*)"Map", {226, 164, 133, 0}},
{(unsigned char*)"Mcy", {208, 156, 0}},
{(unsigned char*)"MediumSpace", {226, 129, 159, 0}},
{(unsigned char*)"Mellintrf", {226, 132, 179, 0}},
{(unsigned char*)"Mfr", {240, 157, 148, 144, 0}},
{(unsigned char*)"MinusPlus", {226, 136, 147, 0}},
{(unsigned char*)"Mopf", {240, 157, 149, 132, 0}},
{(unsigned char*)"Mscr", {226, 132, 179, 0}},
{(unsigned char*)"Mu", {206, 156, 0}},
{(unsigned char*)"NJcy", {208, 138, 0}},
{(unsigned char*)"Nacute", {197, 131, 0}},
{(unsigned char*)"Ncaron", {197, 135, 0}},
{(unsigned char*)"Ncedil", {197, 133, 0}},
{(unsigned char*)"Ncy", {208, 157, 0}},
{(unsigned char*)"NegativeMediumSpace", {226, 128, 139, 0}},
{(unsigned char*)"NegativeThickSpace", {226, 128, 139, 0}},
{(unsigned char*)"NegativeThinSpace", {226, 128, 139, 0}},
{(unsigned char*)"NegativeVeryThinSpace", {226, 128, 139, 0}},
{(unsigned char*)"NestedGreaterGreater", {226, 137, 171, 0}},
{(unsigned char*)"NestedLessLess", {226, 137, 170, 0}},
{(unsigned char*)"NewLine", {10, 0}},
{(unsigned char*)"Nfr", {240, 157, 148, 145, 0}},
{(unsigned char*)"NoBreak", {226, 129, 160, 0}},
{(unsigned char*)"NonBreakingSpace", {194, 160, 0}},
{(unsigned char*)"Nopf", {226, 132, 149, 0}},
{(unsigned char*)"Not", {226, 171, 172, 0}},
{(unsigned char*)"NotCongruent", {226, 137, 162, 0}},
{(unsigned char*)"NotCupCap", {226, 137, 173, 0}},
{(unsigned char*)"NotDoubleVerticalBar", {226, 136, 166, 0}},
{(unsigned char*)"NotElement", {226, 136, 137, 0}},
{(unsigned char*)"NotEqual", {226, 137, 160, 0}},
{(unsigned char*)"NotEqualTilde", {226, 137, 130, 204, 184, 0}},
{(unsigned char*)"NotExists", {226, 136, 132, 0}},
{(unsigned char*)"NotGreater", {226, 137, 175, 0}},
{(unsigned char*)"NotGreaterEqual", {226, 137, 177, 0}},
{(unsigned char*)"NotGreaterFullEqual", {226, 137, 167, 204, 184, 0}},
{(unsigned char*)"NotGreaterGreater", {226, 137, 171, 204, 184, 0}},
{(unsigned char*)"NotGreaterLess", {226, 137, 185, 0}},
{(unsigned char*)"NotGreaterSlantEqual", {226, 169, 190, 204, 184, 0}},
{(unsigned char*)"NotGreaterTilde", {226, 137, 181, 0}},
{(unsigned char*)"NotHumpDownHump", {226, 137, 142, 204, 184, 0}},
{(unsigned char*)"NotHumpEqual", {226, 137, 143, 204, 184, 0}},
{(unsigned char*)"NotLeftTriangle", {226, 139, 170, 0}},
{(unsigned char*)"NotLeftTriangleBar", {226, 167, 143, 204, 184, 0}},
{(unsigned char*)"NotLeftTriangleEqual", {226, 139, 172, 0}},
{(unsigned char*)"NotLess", {226, 137, 174, 0}},
{(unsigned char*)"NotLessEqual", {226, 137, 176, 0}},
{(unsigned char*)"NotLessGreater", {226, 137, 184, 0}},
{(unsigned char*)"NotLessLess", {226, 137, 170, 204, 184, 0}},
{(unsigned char*)"NotLessSlantEqual", {226, 169, 189, 204, 184, 0}},
{(unsigned char*)"NotLessTilde", {226, 137, 180, 0}},
{(unsigned char*)"NotNestedGreaterGreater", {226, 170, 162, 204, 184, 0}},
{(unsigned char*)"NotNestedLessLess", {226, 170, 161, 204, 184, 0}},
{(unsigned char*)"NotPrecedes", {226, 138, 128, 0}},
{(unsigned char*)"NotPrecedesEqual", {226, 170, 175, 204, 184, 0}},
{(unsigned char*)"NotPrecedesSlantEqual", {226, 139, 160, 0}},
{(unsigned char*)"NotReverseElement", {226, 136, 140, 0}},
{(unsigned char*)"NotRightTriangle", {226, 139, 171, 0}},
{(unsigned char*)"NotRightTriangleBar", {226, 167, 144, 204, 184, 0}},
{(unsigned char*)"NotRightTriangleEqual", {226, 139, 173, 0}},
{(unsigned char*)"NotSquareSubset", {226, 138, 143, 204, 184, 0}},
{(unsigned char*)"NotSquareSubsetEqual", {226, 139, 162, 0}},
{(unsigned char*)"NotSquareSuperset", {226, 138, 144, 204, 184, 0}},
{(unsigned char*)"NotSquareSupersetEqual", {226, 139, 163, 0}},
{(unsigned char*)"NotSubset", {226, 138, 130, 226, 131, 146, 0}},
{(unsigned char*)"NotSubsetEqual", {226, 138, 136, 0}},
{(unsigned char*)"NotSucceeds", {226, 138, 129, 0}},
{(unsigned char*)"NotSucceedsEqual", {226, 170, 176, 204, 184, 0}},
{(unsigned char*)"NotSucceedsSlantEqual", {226, 139, 161, 0}},
{(unsigned char*)"NotSucceedsTilde", {226, 137, 191, 204, 184, 0}},
{(unsigned char*)"NotSuperset", {226, 138, 131, 226, 131, 146, 0}},
{(unsigned char*)"NotSupersetEqual", {226, 138, 137, 0}},
{(unsigned char*)"NotTilde", {226, 137, 129, 0}},
{(unsigned char*)"NotTildeEqual", {226, 137, 132, 0}},
{(unsigned char*)"NotTildeFullEqual", {226, 137, 135, 0}},
{(unsigned char*)"NotTildeTilde", {226, 137, 137, 0}},
{(unsigned char*)"NotVerticalBar", {226, 136, 164, 0}},
{(unsigned char*)"Nscr", {240, 157, 146, 169, 0}},
{(unsigned char*)"Ntilde", {195, 145, 0}},
{(unsigned char*)"Nu", {206, 157, 0}},
{(unsigned char*)"OElig", {197, 146, 0}},
{(unsigned char*)"Oacute", {195, 147, 0}},
{(unsigned char*)"Ocirc", {195, 148, 0}},
{(unsigned char*)"Ocy", {208, 158, 0}},
{(unsigned char*)"Odblac", {197, 144, 0}},
{(unsigned char*)"Ofr", {240, 157, 148, 146, 0}},
{(unsigned char*)"Ograve", {195, 146, 0}},
{(unsigned char*)"Omacr", {197, 140, 0}},
{(unsigned char*)"Omega", {206, 169, 0}},
{(unsigned char*)"Omicron", {206, 159, 0}},
{(unsigned char*)"Oopf", {240, 157, 149, 134, 0}},
{(unsigned char*)"OpenCurlyDoubleQuote", {226, 128, 156, 0}},
{(unsigned char*)"OpenCurlyQuote", {226, 128, 152, 0}},
{(unsigned char*)"Or", {226, 169, 148, 0}},
{(unsigned char*)"Oscr", {240, 157, 146, 170, 0}},
{(unsigned char*)"Oslash", {195, 152, 0}},
{(unsigned char*)"Otilde", {195, 149, 0}},
{(unsigned char*)"Otimes", {226, 168, 183, 0}},
{(unsigned char*)"Ouml", {195, 150, 0}},
{(unsigned char*)"OverBar", {226, 128, 190, 0}},
{(unsigned char*)"OverBrace", {226, 143, 158, 0}},
{(unsigned char*)"OverBracket", {226, 142, 180, 0}},
{(unsigned char*)"OverParenthesis", {226, 143, 156, 0}},
{(unsigned char*)"PartialD", {226, 136, 130, 0}},
{(unsigned char*)"Pcy", {208, 159, 0}},
{(unsigned char*)"Pfr", {240, 157, 148, 147, 0}},
{(unsigned char*)"Phi", {206, 166, 0}},
{(unsigned char*)"Pi", {206, 160, 0}},
{(unsigned char*)"PlusMinus", {194, 177, 0}},
{(unsigned char*)"Poincareplane", {226, 132, 140, 0}},
{(unsigned char*)"Popf", {226, 132, 153, 0}},
{(unsigned char*)"Pr", {226, 170, 187, 0}},
{(unsigned char*)"Precedes", {226, 137, 186, 0}},
{(unsigned char*)"PrecedesEqual", {226, 170, 175, 0}},
{(unsigned char*)"PrecedesSlantEqual", {226, 137, 188, 0}},
{(unsigned char*)"PrecedesTilde", {226, 137, 190, 0}},
{(unsigned char*)"Prime", {226, 128, 179, 0}},
{(unsigned char*)"Product", {226, 136, 143, 0}},
{(unsigned char*)"Proportion", {226, 136, 183, 0}},
{(unsigned char*)"Proportional", {226, 136, 157, 0}},
{(unsigned char*)"Pscr", {240, 157, 146, 171, 0}},
{(unsigned char*)"Psi", {206, 168, 0}},
{(unsigned char*)"QUOT", {34, 0}},
{(unsigned char*)"Qfr", {240, 157, 148, 148, 0}},
{(unsigned char*)"Qopf", {226, 132, 154, 0}},
{(unsigned char*)"Qscr", {240, 157, 146, 172, 0}},
{(unsigned char*)"RBarr", {226, 164, 144, 0}},
{(unsigned char*)"REG", {194, 174, 0}},
{(unsigned char*)"Racute", {197, 148, 0}},
{(unsigned char*)"Rang", {226, 159, 171, 0}},
{(unsigned char*)"Rarr", {226, 134, 160, 0}},
{(unsigned char*)"Rarrtl", {226, 164, 150, 0}},
{(unsigned char*)"Rcaron", {197, 152, 0}},
{(unsigned char*)"Rcedil", {197, 150, 0}},
{(unsigned char*)"Rcy", {208, 160, 0}},
{(unsigned char*)"Re", {226, 132, 156, 0}},
{(unsigned char*)"ReverseElement", {226, 136, 139, 0}},
{(unsigned char*)"ReverseEquilibrium", {226, 135, 139, 0}},
{(unsigned char*)"ReverseUpEquilibrium", {226, 165, 175, 0}},
{(unsigned char*)"Rfr", {226, 132, 156, 0}},
{(unsigned char*)"Rho", {206, 161, 0}},
{(unsigned char*)"RightAngleBracket", {226, 159, 169, 0}},
{(unsigned char*)"RightArrow", {226, 134, 146, 0}},
{(unsigned char*)"RightArrowBar", {226, 135, 165, 0}},
{(unsigned char*)"RightArrowLeftArrow", {226, 135, 132, 0}},
{(unsigned char*)"RightCeiling", {226, 140, 137, 0}},
{(unsigned char*)"RightDoubleBracket", {226, 159, 167, 0}},
{(unsigned char*)"RightDownTeeVector", {226, 165, 157, 0}},
{(unsigned char*)"RightDownVector", {226, 135, 130, 0}},
{(unsigned char*)"RightDownVectorBar", {226, 165, 149, 0}},
{(unsigned char*)"RightFloor", {226, 140, 139, 0}},
{(unsigned char*)"RightTee", {226, 138, 162, 0}},
{(unsigned char*)"RightTeeArrow", {226, 134, 166, 0}},
{(unsigned char*)"RightTeeVector", {226, 165, 155, 0}},
{(unsigned char*)"RightTriangle", {226, 138, 179, 0}},
{(unsigned char*)"RightTriangleBar", {226, 167, 144, 0}},
{(unsigned char*)"RightTriangleEqual", {226, 138, 181, 0}},
{(unsigned char*)"RightUpDownVector", {226, 165, 143, 0}},
{(unsigned char*)"RightUpTeeVector", {226, 165, 156, 0}},
{(unsigned char*)"RightUpVector", {226, 134, 190, 0}},
{(unsigned char*)"RightUpVectorBar", {226, 165, 148, 0}},
{(unsigned char*)"RightVector", {226, 135, 128, 0}},
{(unsigned char*)"RightVectorBar", {226, 165, 147, 0}},
{(unsigned char*)"Rightarrow", {226, 135, 146, 0}},
{(unsigned char*)"Ropf", {226, 132, 157, 0}},
{(unsigned char*)"RoundImplies", {226, 165, 176, 0}},
{(unsigned char*)"Rrightarrow", {226, 135, 155, 0}},
{(unsigned char*)"Rscr", {226, 132, 155, 0}},
{(unsigned char*)"Rsh", {226, 134, 177, 0}},
{(unsigned char*)"RuleDelayed", {226, 167, 180, 0}},
{(unsigned char*)"SHCHcy", {208, 169, 0}},
{(unsigned char*)"SHcy", {208, 168, 0}},
{(unsigned char*)"SOFTcy", {208, 172, 0}},
{(unsigned char*)"Sacute", {197, 154, 0}},
{(unsigned char*)"Sc", {226, 170, 188, 0}},
{(unsigned char*)"Scaron", {197, 160, 0}},
{(unsigned char*)"Scedil", {197, 158, 0}},
{(unsigned char*)"Scirc", {197, 156, 0}},
{(unsigned char*)"Scy", {208, 161, 0}},
{(unsigned char*)"Sfr", {240, 157, 148, 150, 0}},
{(unsigned char*)"ShortDownArrow", {226, 134, 147, 0}},
{(unsigned char*)"ShortLeftArrow", {226, 134, 144, 0}},
{(unsigned char*)"ShortRightArrow", {226, 134, 146, 0}},
{(unsigned char*)"ShortUpArrow", {226, 134, 145, 0}},
{(unsigned char*)"Sigma", {206, 163, 0}},
{(unsigned char*)"SmallCircle", {226, 136, 152, 0}},
{(unsigned char*)"Sopf", {240, 157, 149, 138, 0}},
{(unsigned char*)"Sqrt", {226, 136, 154, 0}},
{(unsigned char*)"Square", {226, 150, 161, 0}},
{(unsigned char*)"SquareIntersection", {226, 138, 147, 0}},
{(unsigned char*)"SquareSubset", {226, 138, 143, 0}},
{(unsigned char*)"SquareSubsetEqual", {226, 138, 145, 0}},
{(unsigned char*)"SquareSuperset", {226, 138, 144, 0}},
{(unsigned char*)"SquareSupersetEqual", {226, 138, 146, 0}},
{(unsigned char*)"SquareUnion", {226, 138, 148, 0}},
{(unsigned char*)"Sscr", {240, 157, 146, 174, 0}},
{(unsigned char*)"Star", {226, 139, 134, 0}},
{(unsigned char*)"Sub", {226, 139, 144, 0}},
{(unsigned char*)"Subset", {226, 139, 144, 0}},
{(unsigned char*)"SubsetEqual", {226, 138, 134, 0}},
{(unsigned char*)"Succeeds", {226, 137, 187, 0}},
{(unsigned char*)"SucceedsEqual", {226, 170, 176, 0}},
{(unsigned char*)"SucceedsSlantEqual", {226, 137, 189, 0}},
{(unsigned char*)"SucceedsTilde", {226, 137, 191, 0}},
{(unsigned char*)"SuchThat", {226, 136, 139, 0}},
{(unsigned char*)"Sum", {226, 136, 145, 0}},
{(unsigned char*)"Sup", {226, 139, 145, 0}},
{(unsigned char*)"Superset", {226, 138, 131, 0}},
{(unsigned char*)"SupersetEqual", {226, 138, 135, 0}},
{(unsigned char*)"Supset", {226, 139, 145, 0}},
{(unsigned char*)"THORN", {195, 158, 0}},
{(unsigned char*)"TRADE", {226, 132, 162, 0}},
{(unsigned char*)"TSHcy", {208, 139, 0}},
{(unsigned char*)"TScy", {208, 166, 0}},
{(unsigned char*)"Tab", {9, 0}},
{(unsigned char*)"Tau", {206, 164, 0}},
{(unsigned char*)"Tcaron", {197, 164, 0}},
{(unsigned char*)"Tcedil", {197, 162, 0}},
{(unsigned char*)"Tcy", {208, 162, 0}},
{(unsigned char*)"Tfr", {240, 157, 148, 151, 0}},
{(unsigned char*)"Therefore", {226, 136, 180, 0}},
{(unsigned char*)"Theta", {206, 152, 0}},
{(unsigned char*)"ThickSpace", {226, 129, 159, 226, 128, 138, 0}},
{(unsigned char*)"ThinSpace", {226, 128, 137, 0}},
{(unsigned char*)"Tilde", {226, 136, 188, 0}},
{(unsigned char*)"TildeEqual", {226, 137, 131, 0}},
{(unsigned char*)"TildeFullEqual", {226, 137, 133, 0}},
{(unsigned char*)"TildeTilde", {226, 137, 136, 0}},
{(unsigned char*)"Topf", {240, 157, 149, 139, 0}},
{(unsigned char*)"TripleDot", {226, 131, 155, 0}},
{(unsigned char*)"Tscr", {240, 157, 146, 175, 0}},
{(unsigned char*)"Tstrok", {197, 166, 0}},
{(unsigned char*)"Uacute", {195, 154, 0}},
{(unsigned char*)"Uarr", {226, 134, 159, 0}},
{(unsigned char*)"Uarrocir", {226, 165, 137, 0}},
{(unsigned char*)"Ubrcy", {208, 142, 0}},
{(unsigned char*)"Ubreve", {197, 172, 0}},
{(unsigned char*)"Ucirc", {195, 155, 0}},
{(unsigned char*)"Ucy", {208, 163, 0}},
{(unsigned char*)"Udblac", {197, 176, 0}},
{(unsigned char*)"Ufr", {240, 157, 148, 152, 0}},
{(unsigned char*)"Ugrave", {195, 153, 0}},
{(unsigned char*)"Umacr", {197, 170, 0}},
{(unsigned char*)"UnderBar", {95, 0}},
{(unsigned char*)"UnderBrace", {226, 143, 159, 0}},
{(unsigned char*)"UnderBracket", {226, 142, 181, 0}},
{(unsigned char*)"UnderParenthesis", {226, 143, 157, 0}},
{(unsigned char*)"Union", {226, 139, 131, 0}},
{(unsigned char*)"UnionPlus", {226, 138, 142, 0}},
{(unsigned char*)"Uogon", {197, 178, 0}},
{(unsigned char*)"Uopf", {240, 157, 149, 140, 0}},
{(unsigned char*)"UpArrow", {226, 134, 145, 0}},
{(unsigned char*)"UpArrowBar", {226, 164, 146, 0}},
{(unsigned char*)"UpArrowDownArrow", {226, 135, 133, 0}},
{(unsigned char*)"UpDownArrow", {226, 134, 149, 0}},
{(unsigned char*)"UpEquilibrium", {226, 165, 174, 0}},
{(unsigned char*)"UpTee", {226, 138, 165, 0}},
{(unsigned char*)"UpTeeArrow", {226, 134, 165, 0}},
{(unsigned char*)"Uparrow", {226, 135, 145, 0}},
{(unsigned char*)"Updownarrow", {226, 135, 149, 0}},
{(unsigned char*)"UpperLeftArrow", {226, 134, 150, 0}},
{(unsigned char*)"UpperRightArrow", {226, 134, 151, 0}},
{(unsigned char*)"Upsi", {207, 146, 0}},
{(unsigned char*)"Upsilon", {206, 165, 0}},
{(unsigned char*)"Uring", {197, 174, 0}},
{(unsigned char*)"Uscr", {240, 157, 146, 176, 0}},
{(unsigned char*)"Utilde", {197, 168, 0}},
{(unsigned char*)"Uuml", {195, 156, 0}},
{(unsigned char*)"VDash", {226, 138, 171, 0}},
{(unsigned char*)"Vbar", {226, 171, 171, 0}},
{(unsigned char*)"Vcy", {208, 146, 0}},
{(unsigned char*)"Vdash", {226, 138, 169, 0}},
{(unsigned char*)"Vdashl", {226, 171, 166, 0}},
{(unsigned char*)"Vee", {226, 139, 129, 0}},
{(unsigned char*)"Verbar", {226, 128, 150, 0}},
{(unsigned char*)"Vert", {226, 128, 150, 0}},
{(unsigned char*)"VerticalBar", {226, 136, 163, 0}},
{(unsigned char*)"VerticalLine", {124, 0}},
{(unsigned char*)"VerticalSeparator", {226, 157, 152, 0}},
{(unsigned char*)"VerticalTilde", {226, 137, 128, 0}},
{(unsigned char*)"VeryThinSpace", {226, 128, 138, 0}},
{(unsigned char*)"Vfr", {240, 157, 148, 153, 0}},
{(unsigned char*)"Vopf", {240, 157, 149, 141, 0}},
{(unsigned char*)"Vscr", {240, 157, 146, 177, 0}},
{(unsigned char*)"Vvdash", {226, 138, 170, 0}},
{(unsigned char*)"Wcirc", {197, 180, 0}},
{(unsigned char*)"Wedge", {226, 139, 128, 0}},
{(unsigned char*)"Wfr", {240, 157, 148, 154, 0}},
{(unsigned char*)"Wopf", {240, 157, 149, 142, 0}},
{(unsigned char*)"Wscr", {240, 157, 146, 178, 0}},
{(unsigned char*)"Xfr", {240, 157, 148, 155, 0}},
{(unsigned char*)"Xi", {206, 158, 0}},
{(unsigned char*)"Xopf", {240, 157, 149, 143, 0}},
{(unsigned char*)"Xscr", {240, 157, 146, 179, 0}},
{(unsigned char*)"YAcy", {208, 175, 0}},
{(unsigned char*)"YIcy", {208, 135, 0}},
{(unsigned char*)"YUcy", {208, 174, 0}},
{(unsigned char*)"Yacute", {195, 157, 0}},
{(unsigned char*)"Ycirc", {197, 182, 0}},
{(unsigned char*)"Ycy", {208, 171, 0}},
{(unsigned char*)"Yfr", {240, 157, 148, 156, 0}},
{(unsigned char*)"Yopf", {240, 157, 149, 144, 0}},
{(unsigned char*)"Yscr", {240, 157, 146, 180, 0}},
{(unsigned char*)"Yuml", {197, 184, 0}},
{(unsigned char*)"ZHcy", {208, 150, 0}},
{(unsigned char*)"Zacute", {197, 185, 0}},
{(unsigned char*)"Zcaron", {197, 189, 0}},
{(unsigned char*)"Zcy", {208, 151, 0}},
{(unsigned char*)"Zdot", {197, 187, 0}},
{(unsigned char*)"ZeroWidthSpace", {226, 128, 139, 0}},
{(unsigned char*)"Zeta", {206, 150, 0}},
{(unsigned char*)"Zfr", {226, 132, 168, 0}},
{(unsigned char*)"Zopf", {226, 132, 164, 0}},
{(unsigned char*)"Zscr", {240, 157, 146, 181, 0}},
{(unsigned char*)"aacute", {195, 161, 0}},
{(unsigned char*)"abreve", {196, 131, 0}},
{(unsigned char*)"ac", {226, 136, 190, 0}},
{(unsigned char*)"acE", {226, 136, 190, 204, 179, 0}},
{(unsigned char*)"acd", {226, 136, 191, 0}},
{(unsigned char*)"acirc", {195, 162, 0}},
{(unsigned char*)"acute", {194, 180, 0}},
{(unsigned char*)"acy", {208, 176, 0}},
{(unsigned char*)"aelig", {195, 166, 0}},
{(unsigned char*)"af", {226, 129, 161, 0}},
{(unsigned char*)"afr", {240, 157, 148, 158, 0}},
{(unsigned char*)"agrave", {195, 160, 0}},
{(unsigned char*)"alefsym", {226, 132, 181, 0}},
{(unsigned char*)"aleph", {226, 132, 181, 0}},
{(unsigned char*)"alpha", {206, 177, 0}},
{(unsigned char*)"amacr", {196, 129, 0}},
{(unsigned char*)"amalg", {226, 168, 191, 0}},
{(unsigned char*)"amp", {38, 0}},
{(unsigned char*)"and", {226, 136, 167, 0}},
{(unsigned char*)"andand", {226, 169, 149, 0}},
{(unsigned char*)"andd", {226, 169, 156, 0}},
{(unsigned char*)"andslope", {226, 169, 152, 0}},
{(unsigned char*)"andv", {226, 169, 154, 0}},
{(unsigned char*)"ang", {226, 136, 160, 0}},
{(unsigned char*)"ange", {226, 166, 164, 0}},
{(unsigned char*)"angle", {226, 136, 160, 0}},
{(unsigned char*)"angmsd", {226, 136, 161, 0}},
{(unsigned char*)"angmsdaa", {226, 166, 168, 0}},
{(unsigned char*)"angmsdab", {226, 166, 169, 0}},
{(unsigned char*)"angmsdac", {226, 166, 170, 0}},
{(unsigned char*)"angmsdad", {226, 166, 171, 0}},
{(unsigned char*)"angmsdae", {226, 166, 172, 0}},
{(unsigned char*)"angmsdaf", {226, 166, 173, 0}},
{(unsigned char*)"angmsdag", {226, 166, 174, 0}},
{(unsigned char*)"angmsdah", {226, 166, 175, 0}},
{(unsigned char*)"angrt", {226, 136, 159, 0}},
{(unsigned char*)"angrtvb", {226, 138, 190, 0}},
{(unsigned char*)"angrtvbd", {226, 166, 157, 0}},
{(unsigned char*)"angsph", {226, 136, 162, 0}},
{(unsigned char*)"angst", {195, 133, 0}},
{(unsigned char*)"angzarr", {226, 141, 188, 0}},
{(unsigned char*)"aogon", {196, 133, 0}},
{(unsigned char*)"aopf", {240, 157, 149, 146, 0}},
{(unsigned char*)"ap", {226, 137, 136, 0}},
{(unsigned char*)"apE", {226, 169, 176, 0}},
{(unsigned char*)"apacir", {226, 169, 175, 0}},
{(unsigned char*)"ape", {226, 137, 138, 0}},
{(unsigned char*)"apid", {226, 137, 139, 0}},
{(unsigned char*)"apos", {39, 0}},
{(unsigned char*)"approx", {226, 137, 136, 0}},
{(unsigned char*)"approxeq", {226, 137, 138, 0}},
{(unsigned char*)"aring", {195, 165, 0}},
{(unsigned char*)"ascr", {240, 157, 146, 182, 0}},
{(unsigned char*)"ast", {42, 0}},
{(unsigned char*)"asymp", {226, 137, 136, 0}},
{(unsigned char*)"asympeq", {226, 137, 141, 0}},
{(unsigned char*)"atilde", {195, 163, 0}},
{(unsigned char*)"auml", {195, 164, 0}},
{(unsigned char*)"awconint", {226, 136, 179, 0}},
{(unsigned char*)"awint", {226, 168, 145, 0}},
{(unsigned char*)"bNot", {226, 171, 173, 0}},
{(unsigned char*)"backcong", {226, 137, 140, 0}},
{(unsigned char*)"backepsilon", {207, 182, 0}},
{(unsigned char*)"backprime", {226, 128, 181, 0}},
{(unsigned char*)"backsim", {226, 136, 189, 0}},
{(unsigned char*)"backsimeq", {226, 139, 141, 0}},
{(unsigned char*)"barvee", {226, 138, 189, 0}},
{(unsigned char*)"barwed", {226, 140, 133, 0}},
{(unsigned char*)"barwedge", {226, 140, 133, 0}},
{(unsigned char*)"bbrk", {226, 142, 181, 0}},
{(unsigned char*)"bbrktbrk", {226, 142, 182, 0}},
{(unsigned char*)"bcong", {226, 137, 140, 0}},
{(unsigned char*)"bcy", {208, 177, 0}},
{(unsigned char*)"bdquo", {226, 128, 158, 0}},
{(unsigned char*)"becaus", {226, 136, 181, 0}},
{(unsigned char*)"because", {226, 136, 181, 0}},
{(unsigned char*)"bemptyv", {226, 166, 176, 0}},
{(unsigned char*)"bepsi", {207, 182, 0}},
{(unsigned char*)"bernou", {226, 132, 172, 0}},
{(unsigned char*)"beta", {206, 178, 0}},
{(unsigned char*)"beth", {226, 132, 182, 0}},
{(unsigned char*)"between", {226, 137, 172, 0}},
{(unsigned char*)"bfr", {240, 157, 148, 159, 0}},
{(unsigned char*)"bigcap", {226, 139, 130, 0}},
{(unsigned char*)"bigcirc", {226, 151, 175, 0}},
{(unsigned char*)"bigcup", {226, 139, 131, 0}},
{(unsigned char*)"bigodot", {226, 168, 128, 0}},
{(unsigned char*)"bigoplus", {226, 168, 129, 0}},
{(unsigned char*)"bigotimes", {226, 168, 130, 0}},
{(unsigned char*)"bigsqcup", {226, 168, 134, 0}},
{(unsigned char*)"bigstar", {226, 152, 133, 0}},
{(unsigned char*)"bigtriangledown", {226, 150, 189, 0}},
{(unsigned char*)"bigtriangleup", {226, 150, 179, 0}},
{(unsigned char*)"biguplus", {226, 168, 132, 0}},
{(unsigned char*)"bigvee", {226, 139, 129, 0}},
{(unsigned char*)"bigwedge", {226, 139, 128, 0}},
{(unsigned char*)"bkarow", {226, 164, 141, 0}},
{(unsigned char*)"blacklozenge", {226, 167, 171, 0}},
{(unsigned char*)"blacksquare", {226, 150, 170, 0}},
{(unsigned char*)"blacktriangle", {226, 150, 180, 0}},
{(unsigned char*)"blacktriangledown", {226, 150, 190, 0}},
{(unsigned char*)"blacktriangleleft", {226, 151, 130, 0}},
{(unsigned char*)"blacktriangleright", {226, 150, 184, 0}},
{(unsigned char*)"blank", {226, 144, 163, 0}},
{(unsigned char*)"blk12", {226, 150, 146, 0}},
{(unsigned char*)"blk14", {226, 150, 145, 0}},
{(unsigned char*)"blk34", {226, 150, 147, 0}},
{(unsigned char*)"block", {226, 150, 136, 0}},
{(unsigned char*)"bne", {61, 226, 131, 165, 0}},
{(unsigned char*)"bnequiv", {226, 137, 161, 226, 131, 165, 0}},
{(unsigned char*)"bnot", {226, 140, 144, 0}},
{(unsigned char*)"bopf", {240, 157, 149, 147, 0}},
{(unsigned char*)"bot", {226, 138, 165, 0}},
{(unsigned char*)"bottom", {226, 138, 165, 0}},
{(unsigned char*)"bowtie", {226, 139, 136, 0}},
{(unsigned char*)"boxDL", {226, 149, 151, 0}},
{(unsigned char*)"boxDR", {226, 149, 148, 0}},
{(unsigned char*)"boxDl", {226, 149, 150, 0}},
{(unsigned char*)"boxDr", {226, 149, 147, 0}},
{(unsigned char*)"boxH", {226, 149, 144, 0}},
{(unsigned char*)"boxHD", {226, 149, 166, 0}},
{(unsigned char*)"boxHU", {226, 149, 169, 0}},
{(unsigned char*)"boxHd", {226, 149, 164, 0}},
{(unsigned char*)"boxHu", {226, 149, 167, 0}},
{(unsigned char*)"boxUL", {226, 149, 157, 0}},
{(unsigned char*)"boxUR", {226, 149, 154, 0}},
{(unsigned char*)"boxUl", {226, 149, 156, 0}},
{(unsigned char*)"boxUr", {226, 149, 153, 0}},
{(unsigned char*)"boxV", {226, 149, 145, 0}},
{(unsigned char*)"boxVH", {226, 149, 172, 0}},
{(unsigned char*)"boxVL", {226, 149, 163, 0}},
{(unsigned char*)"boxVR", {226, 149, 160, 0}},
{(unsigned char*)"boxVh", {226, 149, 171, 0}},
{(unsigned char*)"boxVl", {226, 149, 162, 0}},
{(unsigned char*)"boxVr", {226, 149, 159, 0}},
{(unsigned char*)"boxbox", {226, 167, 137, 0}},
{(unsigned char*)"boxdL", {226, 149, 149, 0}},
{(unsigned char*)"boxdR", {226, 149, 146, 0}},
{(unsigned char*)"boxdl", {226, 148, 144, 0}},
{(unsigned char*)"boxdr", {226, 148, 140, 0}},
{(unsigned char*)"boxh", {226, 148, 128, 0}},
{(unsigned char*)"boxhD", {226, 149, 165, 0}},
{(unsigned char*)"boxhU", {226, 149, 168, 0}},
{(unsigned char*)"boxhd", {226, 148, 172, 0}},
{(unsigned char*)"boxhu", {226, 148, 180, 0}},
{(unsigned char*)"boxminus", {226, 138, 159, 0}},
{(unsigned char*)"boxplus", {226, 138, 158, 0}},
{(unsigned char*)"boxtimes", {226, 138, 160, 0}},
{(unsigned char*)"boxuL", {226, 149, 155, 0}},
{(unsigned char*)"boxuR", {226, 149, 152, 0}},
{(unsigned char*)"boxul", {226, 148, 152, 0}},
{(unsigned char*)"boxur", {226, 148, 148, 0}},
{(unsigned char*)"boxv", {226, 148, 130, 0}},
{(unsigned char*)"boxvH", {226, 149, 170, 0}},
{(unsigned char*)"boxvL", {226, 149, 161, 0}},
{(unsigned char*)"boxvR", {226, 149, 158, 0}},
{(unsigned char*)"boxvh", {226, 148, 188, 0}},
{(unsigned char*)"boxvl", {226, 148, 164, 0}},
{(unsigned char*)"boxvr", {226, 148, 156, 0}},
{(unsigned char*)"bprime", {226, 128, 181, 0}},
{(unsigned char*)"breve", {203, 152, 0}},
{(unsigned char*)"brvbar", {194, 166, 0}},
{(unsigned char*)"bscr", {240, 157, 146, 183, 0}},
{(unsigned char*)"bsemi", {226, 129, 143, 0}},
{(unsigned char*)"bsim", {226, 136, 189, 0}},
{(unsigned char*)"bsime", {226, 139, 141, 0}},
{(unsigned char*)"bsol", {92, 0}},
{(unsigned char*)"bsolb", {226, 167, 133, 0}},
{(unsigned char*)"bsolhsub", {226, 159, 136, 0}},
{(unsigned char*)"bull", {226, 128, 162, 0}},
{(unsigned char*)"bullet", {226, 128, 162, 0}},
{(unsigned char*)"bump", {226, 137, 142, 0}},
{(unsigned char*)"bumpE", {226, 170, 174, 0}},
{(unsigned char*)"bumpe", {226, 137, 143, 0}},
{(unsigned char*)"bumpeq", {226, 137, 143, 0}},
{(unsigned char*)"cacute", {196, 135, 0}},
{(unsigned char*)"cap", {226, 136, 169, 0}},
{(unsigned char*)"capand", {226, 169, 132, 0}},
{(unsigned char*)"capbrcup", {226, 169, 137, 0}},
{(unsigned char*)"capcap", {226, 169, 139, 0}},
{(unsigned char*)"capcup", {226, 169, 135, 0}},
{(unsigned char*)"capdot", {226, 169, 128, 0}},
{(unsigned char*)"caps", {226, 136, 169, 239, 184, 128, 0}},
{(unsigned char*)"caret", {226, 129, 129, 0}},
{(unsigned char*)"caron", {203, 135, 0}},
{(unsigned char*)"ccaps", {226, 169, 141, 0}},
{(unsigned char*)"ccaron", {196, 141, 0}},
{(unsigned char*)"ccedil", {195, 167, 0}},
{(unsigned char*)"ccirc", {196, 137, 0}},
{(unsigned char*)"ccups", {226, 169, 140, 0}},
{(unsigned char*)"ccupssm", {226, 169, 144, 0}},
{(unsigned char*)"cdot", {196, 139, 0}},
{(unsigned char*)"cedil", {194, 184, 0}},
{(unsigned char*)"cemptyv", {226, 166, 178, 0}},
{(unsigned char*)"cent", {194, 162, 0}},
{(unsigned char*)"centerdot", {194, 183, 0}},
{(unsigned char*)"cfr", {240, 157, 148, 160, 0}},
{(unsigned char*)"chcy", {209, 135, 0}},
{(unsigned char*)"check", {226, 156, 147, 0}},
{(unsigned char*)"checkmark", {226, 156, 147, 0}},
{(unsigned char*)"chi", {207, 135, 0}},
{(unsigned char*)"cir", {226, 151, 139, 0}},
{(unsigned char*)"cirE", {226, 167, 131, 0}},
{(unsigned char*)"circ", {203, 134, 0}},
{(unsigned char*)"circeq", {226, 137, 151, 0}},
{(unsigned char*)"circlearrowleft", {226, 134, 186, 0}},
{(unsigned char*)"circlearrowright", {226, 134, 187, 0}},
{(unsigned char*)"circledR", {194, 174, 0}},
{(unsigned char*)"circledS", {226, 147, 136, 0}},
{(unsigned char*)"circledast", {226, 138, 155, 0}},
{(unsigned char*)"circledcirc", {226, 138, 154, 0}},
{(unsigned char*)"circleddash", {226, 138, 157, 0}},
{(unsigned char*)"cire", {226, 137, 151, 0}},
{(unsigned char*)"cirfnint", {226, 168, 144, 0}},
{(unsigned char*)"cirmid", {226, 171, 175, 0}},
{(unsigned char*)"cirscir", {226, 167, 130, 0}},
{(unsigned char*)"clubs", {226, 153, 163, 0}},
{(unsigned char*)"clubsuit", {226, 153, 163, 0}},
{(unsigned char*)"colon", {58, 0}},
{(unsigned char*)"colone", {226, 137, 148, 0}},
{(unsigned char*)"coloneq", {226, 137, 148, 0}},
{(unsigned char*)"comma", {44, 0}},
{(unsigned char*)"commat", {64, 0}},
{(unsigned char*)"comp", {226, 136, 129, 0}},
{(unsigned char*)"compfn", {226, 136, 152, 0}},
{(unsigned char*)"complement", {226, 136, 129, 0}},
{(unsigned char*)"complexes", {226, 132, 130, 0}},
{(unsigned char*)"cong", {226, 137, 133, 0}},
{(unsigned char*)"congdot", {226, 169, 173, 0}},
{(unsigned char*)"conint", {226, 136, 174, 0}},
{(unsigned char*)"copf", {240, 157, 149, 148, 0}},
{(unsigned char*)"coprod", {226, 136, 144, 0}},
{(unsigned char*)"copy", {194, 169, 0}},
{(unsigned char*)"copysr", {226, 132, 151, 0}},
{(unsigned char*)"crarr", {226, 134, 181, 0}},
{(unsigned char*)"cross", {226, 156, 151, 0}},
{(unsigned char*)"cscr", {240, 157, 146, 184, 0}},
{(unsigned char*)"csub", {226, 171, 143, 0}},
{(unsigned char*)"csube", {226, 171, 145, 0}},
{(unsigned char*)"csup", {226, 171, 144, 0}},
{(unsigned char*)"csupe", {226, 171, 146, 0}},
{(unsigned char*)"ctdot", {226, 139, 175, 0}},
{(unsigned char*)"cudarrl", {226, 164, 184, 0}},
{(unsigned char*)"cudarrr", {226, 164, 181, 0}},
{(unsigned char*)"cuepr", {226, 139, 158, 0}},
{(unsigned char*)"cuesc", {226, 139, 159, 0}},
{(unsigned char*)"cularr", {226, 134, 182, 0}},
{(unsigned char*)"cularrp", {226, 164, 189, 0}},
{(unsigned char*)"cup", {226, 136, 170, 0}},
{(unsigned char*)"cupbrcap", {226, 169, 136, 0}},
{(unsigned char*)"cupcap", {226, 169, 134, 0}},
{(unsigned char*)"cupcup", {226, 169, 138, 0}},
{(unsigned char*)"cupdot", {226, 138, 141, 0}},
{(unsigned char*)"cupor", {226, 169, 133, 0}},
{(unsigned char*)"cups", {226, 136, 170, 239, 184, 128, 0}},
{(unsigned char*)"curarr", {226, 134, 183, 0}},
{(unsigned char*)"curarrm", {226, 164, 188, 0}},
{(unsigned char*)"curlyeqprec", {226, 139, 158, 0}},
{(unsigned char*)"curlyeqsucc", {226, 139, 159, 0}},
{(unsigned char*)"curlyvee", {226, 139, 142, 0}},
{(unsigned char*)"curlywedge", {226, 139, 143, 0}},
{(unsigned char*)"curren", {194, 164, 0}},
{(unsigned char*)"curvearrowleft", {226, 134, 182, 0}},
{(unsigned char*)"curvearrowright", {226, 134, 183, 0}},
{(unsigned char*)"cuvee", {226, 139, 142, 0}},
{(unsigned char*)"cuwed", {226, 139, 143, 0}},
{(unsigned char*)"cwconint", {226, 136, 178, 0}},
{(unsigned char*)"cwint", {226, 136, 177, 0}},
{(unsigned char*)"cylcty", {226, 140, 173, 0}},
{(unsigned char*)"dArr", {226, 135, 147, 0}},
{(unsigned char*)"dHar", {226, 165, 165, 0}},
{(unsigned char*)"dagger", {226, 128, 160, 0}},
{(unsigned char*)"daleth", {226, 132, 184, 0}},
{(unsigned char*)"darr", {226, 134, 147, 0}},
{(unsigned char*)"dash", {226, 128, 144, 0}},
{(unsigned char*)"dashv", {226, 138, 163, 0}},
{(unsigned char*)"dbkarow", {226, 164, 143, 0}},
{(unsigned char*)"dblac", {203, 157, 0}},
{(unsigned char*)"dcaron", {196, 143, 0}},
{(unsigned char*)"dcy", {208, 180, 0}},
{(unsigned char*)"dd", {226, 133, 134, 0}},
{(unsigned char*)"ddagger", {226, 128, 161, 0}},
{(unsigned char*)"ddarr", {226, 135, 138, 0}},
{(unsigned char*)"ddotseq", {226, 169, 183, 0}},
{(unsigned char*)"deg", {194, 176, 0}},
{(unsigned char*)"delta", {206, 180, 0}},
{(unsigned char*)"demptyv", {226, 166, 177, 0}},
{(unsigned char*)"dfisht", {226, 165, 191, 0}},
{(unsigned char*)"dfr", {240, 157, 148, 161, 0}},
{(unsigned char*)"dharl", {226, 135, 131, 0}},
{(unsigned char*)"dharr", {226, 135, 130, 0}},
{(unsigned char*)"diam", {226, 139, 132, 0}},
{(unsigned char*)"diamond", {226, 139, 132, 0}},
{(unsigned char*)"diamondsuit", {226, 153, 166, 0}},
{(unsigned char*)"diams", {226, 153, 166, 0}},
{(unsigned char*)"die", {194, 168, 0}},
{(unsigned char*)"digamma", {207, 157, 0}},
{(unsigned char*)"disin", {226, 139, 178, 0}},
{(unsigned char*)"div", {195, 183, 0}},
{(unsigned char*)"divide", {195, 183, 0}},
{(unsigned char*)"divideontimes", {226, 139, 135, 0}},
{(unsigned char*)"divonx", {226, 139, 135, 0}},
{(unsigned char*)"djcy", {209, 146, 0}},
{(unsigned char*)"dlcorn", {226, 140, 158, 0}},
{(unsigned char*)"dlcrop", {226, 140, 141, 0}},
{(unsigned char*)"dollar", {36, 0}},
{(unsigned char*)"dopf", {240, 157, 149, 149, 0}},
{(unsigned char*)"dot", {203, 153, 0}},
{(unsigned char*)"doteq", {226, 137, 144, 0}},
{(unsigned char*)"doteqdot", {226, 137, 145, 0}},
{(unsigned char*)"dotminus", {226, 136, 184, 0}},
{(unsigned char*)"dotplus", {226, 136, 148, 0}},
{(unsigned char*)"dotsquare", {226, 138, 161, 0}},
{(unsigned char*)"doublebarwedge", {226, 140, 134, 0}},
{(unsigned char*)"downarrow", {226, 134, 147, 0}},
{(unsigned char*)"downdownarrows", {226, 135, 138, 0}},
{(unsigned char*)"downharpoonleft", {226, 135, 131, 0}},
{(unsigned char*)"downharpoonright", {226, 135, 130, 0}},
{(unsigned char*)"drbkarow", {226, 164, 144, 0}},
{(unsigned char*)"drcorn", {226, 140, 159, 0}},
{(unsigned char*)"drcrop", {226, 140, 140, 0}},
{(unsigned char*)"dscr", {240, 157, 146, 185, 0}},
{(unsigned char*)"dscy", {209, 149, 0}},
{(unsigned char*)"dsol", {226, 167, 182, 0}},
{(unsigned char*)"dstrok", {196, 145, 0}},
{(unsigned char*)"dtdot", {226, 139, 177, 0}},
{(unsigned char*)"dtri", {226, 150, 191, 0}},
{(unsigned char*)"dtrif", {226, 150, 190, 0}},
{(unsigned char*)"duarr", {226, 135, 181, 0}},
{(unsigned char*)"duhar", {226, 165, 175, 0}},
{(unsigned char*)"dwangle", {226, 166, 166, 0}},
{(unsigned char*)"dzcy", {209, 159, 0}},
{(unsigned char*)"dzigrarr", {226, 159, 191, 0}},
{(unsigned char*)"eDDot", {226, 169, 183, 0}},
{(unsigned char*)"eDot", {226, 137, 145, 0}},
{(unsigned char*)"eacute", {195, 169, 0}},
{(unsigned char*)"easter", {226, 169, 174, 0}},
{(unsigned char*)"ecaron", {196, 155, 0}},
{(unsigned char*)"ecir", {226, 137, 150, 0}},
{(unsigned char*)"ecirc", {195, 170, 0}},
{(unsigned char*)"ecolon", {226, 137, 149, 0}},
{(unsigned char*)"ecy", {209, 141, 0}},
{(unsigned char*)"edot", {196, 151, 0}},
{(unsigned char*)"ee", {226, 133, 135, 0}},
{(unsigned char*)"efDot", {226, 137, 146, 0}},
{(unsigned char*)"efr", {240, 157, 148, 162, 0}},
{(unsigned char*)"eg", {226, 170, 154, 0}},
{(unsigned char*)"egrave", {195, 168, 0}},
{(unsigned char*)"egs", {226, 170, 150, 0}},
{(unsigned char*)"egsdot", {226, 170, 152, 0}},
{(unsigned char*)"el", {226, 170, 153, 0}},
{(unsigned char*)"elinters", {226, 143, 167, 0}},
{(unsigned char*)"ell", {226, 132, 147, 0}},
{(unsigned char*)"els", {226, 170, 149, 0}},
{(unsigned char*)"elsdot", {226, 170, 151, 0}},
{(unsigned char*)"emacr", {196, 147, 0}},
{(unsigned char*)"empty", {226, 136, 133, 0}},
{(unsigned char*)"emptyset", {226, 136, 133, 0}},
{(unsigned char*)"emptyv", {226, 136, 133, 0}},
{(unsigned char*)"emsp", {226, 128, 131, 0}},
{(unsigned char*)"emsp13", {226, 128, 132, 0}},
{(unsigned char*)"emsp14", {226, 128, 133, 0}},
{(unsigned char*)"eng", {197, 139, 0}},
{(unsigned char*)"ensp", {226, 128, 130, 0}},
{(unsigned char*)"eogon", {196, 153, 0}},
{(unsigned char*)"eopf", {240, 157, 149, 150, 0}},
{(unsigned char*)"epar", {226, 139, 149, 0}},
{(unsigned char*)"eparsl", {226, 167, 163, 0}},
{(unsigned char*)"eplus", {226, 169, 177, 0}},
{(unsigned char*)"epsi", {206, 181, 0}},
{(unsigned char*)"epsilon", {206, 181, 0}},
{(unsigned char*)"epsiv", {207, 181, 0}},
{(unsigned char*)"eqcirc", {226, 137, 150, 0}},
{(unsigned char*)"eqcolon", {226, 137, 149, 0}},
{(unsigned char*)"eqsim", {226, 137, 130, 0}},
{(unsigned char*)"eqslantgtr", {226, 170, 150, 0}},
{(unsigned char*)"eqslantless", {226, 170, 149, 0}},
{(unsigned char*)"equals", {61, 0}},
{(unsigned char*)"equest", {226, 137, 159, 0}},
{(unsigned char*)"equiv", {226, 137, 161, 0}},
{(unsigned char*)"equivDD", {226, 169, 184, 0}},
{(unsigned char*)"eqvparsl", {226, 167, 165, 0}},
{(unsigned char*)"erDot", {226, 137, 147, 0}},
{(unsigned char*)"erarr", {226, 165, 177, 0}},
{(unsigned char*)"escr", {226, 132, 175, 0}},
{(unsigned char*)"esdot", {226, 137, 144, 0}},
{(unsigned char*)"esim", {226, 137, 130, 0}},
{(unsigned char*)"eta", {206, 183, 0}},
{(unsigned char*)"eth", {195, 176, 0}},
{(unsigned char*)"euml", {195, 171, 0}},
{(unsigned char*)"euro", {226, 130, 172, 0}},
{(unsigned char*)"excl", {33, 0}},
{(unsigned char*)"exist", {226, 136, 131, 0}},
{(unsigned char*)"expectation", {226, 132, 176, 0}},
{(unsigned char*)"exponentiale", {226, 133, 135, 0}},
{(unsigned char*)"fallingdotseq", {226, 137, 146, 0}},
{(unsigned char*)"fcy", {209, 132, 0}},
{(unsigned char*)"female", {226, 153, 128, 0}},
{(unsigned char*)"ffilig", {239, 172, 131, 0}},
{(unsigned char*)"fflig", {239, 172, 128, 0}},
{(unsigned char*)"ffllig", {239, 172, 132, 0}},
{(unsigned char*)"ffr", {240, 157, 148, 163, 0}},
{(unsigned char*)"filig", {239, 172, 129, 0}},
{(unsigned char*)"fjlig", {102, 106, 0}},
{(unsigned char*)"flat", {226, 153, 173, 0}},
{(unsigned char*)"fllig", {239, 172, 130, 0}},
{(unsigned char*)"fltns", {226, 150, 177, 0}},
{(unsigned char*)"fnof", {198, 146, 0}},
{(unsigned char*)"fopf", {240, 157, 149, 151, 0}},
{(unsigned char*)"forall", {226, 136, 128, 0}},
{(unsigned char*)"fork", {226, 139, 148, 0}},
{(unsigned char*)"forkv", {226, 171, 153, 0}},
{(unsigned char*)"fpartint", {226, 168, 141, 0}},
{(unsigned char*)"frac12", {194, 189, 0}},
{(unsigned char*)"frac13", {226, 133, 147, 0}},
{(unsigned char*)"frac14", {194, 188, 0}},
{(unsigned char*)"frac15", {226, 133, 149, 0}},
{(unsigned char*)"frac16", {226, 133, 153, 0}},
{(unsigned char*)"frac18", {226, 133, 155, 0}},
{(unsigned char*)"frac23", {226, 133, 148, 0}},
{(unsigned char*)"frac25", {226, 133, 150, 0}},
{(unsigned char*)"frac34", {194, 190, 0}},
{(unsigned char*)"frac35", {226, 133, 151, 0}},
{(unsigned char*)"frac38", {226, 133, 156, 0}},
{(unsigned char*)"frac45", {226, 133, 152, 0}},
{(unsigned char*)"frac56", {226, 133, 154, 0}},
{(unsigned char*)"frac58", {226, 133, 157, 0}},
{(unsigned char*)"frac78", {226, 133, 158, 0}},
{(unsigned char*)"frasl", {226, 129, 132, 0}},
{(unsigned char*)"frown", {226, 140, 162, 0}},
{(unsigned char*)"fscr", {240, 157, 146, 187, 0}},
{(unsigned char*)"gE", {226, 137, 167, 0}},
{(unsigned char*)"gEl", {226, 170, 140, 0}},
{(unsigned char*)"gacute", {199, 181, 0}},
{(unsigned char*)"gamma", {206, 179, 0}},
{(unsigned char*)"gammad", {207, 157, 0}},
{(unsigned char*)"gap", {226, 170, 134, 0}},
{(unsigned char*)"gbreve", {196, 159, 0}},
{(unsigned char*)"gcirc", {196, 157, 0}},
{(unsigned char*)"gcy", {208, 179, 0}},
{(unsigned char*)"gdot", {196, 161, 0}},
{(unsigned char*)"ge", {226, 137, 165, 0}},
{(unsigned char*)"gel", {226, 139, 155, 0}},
{(unsigned char*)"geq", {226, 137, 165, 0}},
{(unsigned char*)"geqq", {226, 137, 167, 0}},
{(unsigned char*)"geqslant", {226, 169, 190, 0}},
{(unsigned char*)"ges", {226, 169, 190, 0}},
{(unsigned char*)"gescc", {226, 170, 169, 0}},
{(unsigned char*)"gesdot", {226, 170, 128, 0}},
{(unsigned char*)"gesdoto", {226, 170, 130, 0}},
{(unsigned char*)"gesdotol", {226, 170, 132, 0}},
{(unsigned char*)"gesl", {226, 139, 155, 239, 184, 128, 0}},
{(unsigned char*)"gesles", {226, 170, 148, 0}},
{(unsigned char*)"gfr", {240, 157, 148, 164, 0}},
{(unsigned char*)"gg", {226, 137, 171, 0}},
{(unsigned char*)"ggg", {226, 139, 153, 0}},
{(unsigned char*)"gimel", {226, 132, 183, 0}},
{(unsigned char*)"gjcy", {209, 147, 0}},
{(unsigned char*)"gl", {226, 137, 183, 0}},
{(unsigned char*)"glE", {226, 170, 146, 0}},
{(unsigned char*)"gla", {226, 170, 165, 0}},
{(unsigned char*)"glj", {226, 170, 164, 0}},
{(unsigned char*)"gnE", {226, 137, 169, 0}},
{(unsigned char*)"gnap", {226, 170, 138, 0}},
{(unsigned char*)"gnapprox", {226, 170, 138, 0}},
{(unsigned char*)"gne", {226, 170, 136, 0}},
{(unsigned char*)"gneq", {226, 170, 136, 0}},
{(unsigned char*)"gneqq", {226, 137, 169, 0}},
{(unsigned char*)"gnsim", {226, 139, 167, 0}},
{(unsigned char*)"gopf", {240, 157, 149, 152, 0}},
{(unsigned char*)"grave", {96, 0}},
{(unsigned char*)"gscr", {226, 132, 138, 0}},
{(unsigned char*)"gsim", {226, 137, 179, 0}},
{(unsigned char*)"gsime", {226, 170, 142, 0}},
{(unsigned char*)"gsiml", {226, 170, 144, 0}},
{(unsigned char*)"gt", {62, 0}},
{(unsigned char*)"gtcc", {226, 170, 167, 0}},
{(unsigned char*)"gtcir", {226, 169, 186, 0}},
{(unsigned char*)"gtdot", {226, 139, 151, 0}},
{(unsigned char*)"gtlPar", {226, 166, 149, 0}},
{(unsigned char*)"gtquest", {226, 169, 188, 0}},
{(unsigned char*)"gtrapprox", {226, 170, 134, 0}},
{(unsigned char*)"gtrarr", {226, 165, 184, 0}},
{(unsigned char*)"gtrdot", {226, 139, 151, 0}},
{(unsigned char*)"gtreqless", {226, 139, 155, 0}},
{(unsigned char*)"gtreqqless", {226, 170, 140, 0}},
{(unsigned char*)"gtrless", {226, 137, 183, 0}},
{(unsigned char*)"gtrsim", {226, 137, 179, 0}},
{(unsigned char*)"gvertneqq", {226, 137, 169, 239, 184, 128, 0}},
{(unsigned char*)"gvnE", {226, 137, 169, 239, 184, 128, 0}},
{(unsigned char*)"hArr", {226, 135, 148, 0}},
{(unsigned char*)"hairsp", {226, 128, 138, 0}},
{(unsigned char*)"half", {194, 189, 0}},
{(unsigned char*)"hamilt", {226, 132, 139, 0}},
{(unsigned char*)"hardcy", {209, 138, 0}},
{(unsigned char*)"harr", {226, 134, 148, 0}},
{(unsigned char*)"harrcir", {226, 165, 136, 0}},
{(unsigned char*)"harrw", {226, 134, 173, 0}},
{(unsigned char*)"hbar", {226, 132, 143, 0}},
{(unsigned char*)"hcirc", {196, 165, 0}},
{(unsigned char*)"hearts", {226, 153, 165, 0}},
{(unsigned char*)"heartsuit", {226, 153, 165, 0}},
{(unsigned char*)"hellip", {226, 128, 166, 0}},
{(unsigned char*)"hercon", {226, 138, 185, 0}},
{(unsigned char*)"hfr", {240, 157, 148, 165, 0}},
{(unsigned char*)"hksearow", {226, 164, 165, 0}},
{(unsigned char*)"hkswarow", {226, 164, 166, 0}},
{(unsigned char*)"hoarr", {226, 135, 191, 0}},
{(unsigned char*)"homtht", {226, 136, 187, 0}},
{(unsigned char*)"hookleftarrow", {226, 134, 169, 0}},
{(unsigned char*)"hookrightarrow", {226, 134, 170, 0}},
{(unsigned char*)"hopf", {240, 157, 149, 153, 0}},
{(unsigned char*)"horbar", {226, 128, 149, 0}},
{(unsigned char*)"hscr", {240, 157, 146, 189, 0}},
{(unsigned char*)"hslash", {226, 132, 143, 0}},
{(unsigned char*)"hstrok", {196, 167, 0}},
{(unsigned char*)"hybull", {226, 129, 131, 0}},
{(unsigned char*)"hyphen", {226, 128, 144, 0}},
{(unsigned char*)"iacute", {195, 173, 0}},
{(unsigned char*)"ic", {226, 129, 163, 0}},
{(unsigned char*)"icirc", {195, 174, 0}},
{(unsigned char*)"icy", {208, 184, 0}},
{(unsigned char*)"iecy", {208, 181, 0}},
{(unsigned char*)"iexcl", {194, 161, 0}},
{(unsigned char*)"iff", {226, 135, 148, 0}},
{(unsigned char*)"ifr", {240, 157, 148, 166, 0}},
{(unsigned char*)"igrave", {195, 172, 0}},
{(unsigned char*)"ii", {226, 133, 136, 0}},
{(unsigned char*)"iiiint", {226, 168, 140, 0}},
{(unsigned char*)"iiint", {226, 136, 173, 0}},
{(unsigned char*)"iinfin", {226, 167, 156, 0}},
{(unsigned char*)"iiota", {226, 132, 169, 0}},
{(unsigned char*)"ijlig", {196, 179, 0}},
{(unsigned char*)"imacr", {196, 171, 0}},
{(unsigned char*)"image", {226, 132, 145, 0}},
{(unsigned char*)"imagline", {226, 132, 144, 0}},
{(unsigned char*)"imagpart", {226, 132, 145, 0}},
{(unsigned char*)"imath", {196, 177, 0}},
{(unsigned char*)"imof", {226, 138, 183, 0}},
{(unsigned char*)"imped", {198, 181, 0}},
{(unsigned char*)"in", {226, 136, 136, 0}},
{(unsigned char*)"incare", {226, 132, 133, 0}},
{(unsigned char*)"infin", {226, 136, 158, 0}},
{(unsigned char*)"infintie", {226, 167, 157, 0}},
{(unsigned char*)"inodot", {196, 177, 0}},
{(unsigned char*)"int", {226, 136, 171, 0}},
{(unsigned char*)"intcal", {226, 138, 186, 0}},
{(unsigned char*)"integers", {226, 132, 164, 0}},
{(unsigned char*)"intercal", {226, 138, 186, 0}},
{(unsigned char*)"intlarhk", {226, 168, 151, 0}},
{(unsigned char*)"intprod", {226, 168, 188, 0}},
{(unsigned char*)"iocy", {209, 145, 0}},
{(unsigned char*)"iogon", {196, 175, 0}},
{(unsigned char*)"iopf", {240, 157, 149, 154, 0}},
{(unsigned char*)"iota", {206, 185, 0}},
{(unsigned char*)"iprod", {226, 168, 188, 0}},
{(unsigned char*)"iquest", {194, 191, 0}},
{(unsigned char*)"iscr", {240, 157, 146, 190, 0}},
{(unsigned char*)"isin", {226, 136, 136, 0}},
{(unsigned char*)"isinE", {226, 139, 185, 0}},
{(unsigned char*)"isindot", {226, 139, 181, 0}},
{(unsigned char*)"isins", {226, 139, 180, 0}},
{(unsigned char*)"isinsv", {226, 139, 179, 0}},
{(unsigned char*)"isinv", {226, 136, 136, 0}},
{(unsigned char*)"it", {226, 129, 162, 0}},
{(unsigned char*)"itilde", {196, 169, 0}},
{(unsigned char*)"iukcy", {209, 150, 0}},
{(unsigned char*)"iuml", {195, 175, 0}},
{(unsigned char*)"jcirc", {196, 181, 0}},
{(unsigned char*)"jcy", {208, 185, 0}},
{(unsigned char*)"jfr", {240, 157, 148, 167, 0}},
{(unsigned char*)"jmath", {200, 183, 0}},
{(unsigned char*)"jopf", {240, 157, 149, 155, 0}},
{(unsigned char*)"jscr", {240, 157, 146, 191, 0}},
{(unsigned char*)"jsercy", {209, 152, 0}},
{(unsigned char*)"jukcy", {209, 148, 0}},
{(unsigned char*)"kappa", {206, 186, 0}},
{(unsigned char*)"kappav", {207, 176, 0}},
{(unsigned char*)"kcedil", {196, 183, 0}},
{(unsigned char*)"kcy", {208, 186, 0}},
{(unsigned char*)"kfr", {240, 157, 148, 168, 0}},
{(unsigned char*)"kgreen", {196, 184, 0}},
{(unsigned char*)"khcy", {209, 133, 0}},
{(unsigned char*)"kjcy", {209, 156, 0}},
{(unsigned char*)"kopf", {240, 157, 149, 156, 0}},
{(unsigned char*)"kscr", {240, 157, 147, 128, 0}},
{(unsigned char*)"lAarr", {226, 135, 154, 0}},
{(unsigned char*)"lArr", {226, 135, 144, 0}},
{(unsigned char*)"lAtail", {226, 164, 155, 0}},
{(unsigned char*)"lBarr", {226, 164, 142, 0}},
{(unsigned char*)"lE", {226, 137, 166, 0}},
{(unsigned char*)"lEg", {226, 170, 139, 0}},
{(unsigned char*)"lHar", {226, 165, 162, 0}},
{(unsigned char*)"lacute", {196, 186, 0}},
{(unsigned char*)"laemptyv", {226, 166, 180, 0}},
{(unsigned char*)"lagran", {226, 132, 146, 0}},
{(unsigned char*)"lambda", {206, 187, 0}},
{(unsigned char*)"lang", {226, 159, 168, 0}},
{(unsigned char*)"langd", {226, 166, 145, 0}},
{(unsigned char*)"langle", {226, 159, 168, 0}},
{(unsigned char*)"lap", {226, 170, 133, 0}},
{(unsigned char*)"laquo", {194, 171, 0}},
{(unsigned char*)"larr", {226, 134, 144, 0}},
{(unsigned char*)"larrb", {226, 135, 164, 0}},
{(unsigned char*)"larrbfs", {226, 164, 159, 0}},
{(unsigned char*)"larrfs", {226, 164, 157, 0}},
{(unsigned char*)"larrhk", {226, 134, 169, 0}},
{(unsigned char*)"larrlp", {226, 134, 171, 0}},
{(unsigned char*)"larrpl", {226, 164, 185, 0}},
{(unsigned char*)"larrsim", {226, 165, 179, 0}},
{(unsigned char*)"larrtl", {226, 134, 162, 0}},
{(unsigned char*)"lat", {226, 170, 171, 0}},
{(unsigned char*)"latail", {226, 164, 153, 0}},
{(unsigned char*)"late", {226, 170, 173, 0}},
{(unsigned char*)"lates", {226, 170, 173, 239, 184, 128, 0}},
{(unsigned char*)"lbarr", {226, 164, 140, 0}},
{(unsigned char*)"lbbrk", {226, 157, 178, 0}},
{(unsigned char*)"lbrace", {123, 0}},
{(unsigned char*)"lbrack", {91, 0}},
{(unsigned char*)"lbrke", {226, 166, 139, 0}},
{(unsigned char*)"lbrksld", {226, 166, 143, 0}},
{(unsigned char*)"lbrkslu", {226, 166, 141, 0}},
{(unsigned char*)"lcaron", {196, 190, 0}},
{(unsigned char*)"lcedil", {196, 188, 0}},
{(unsigned char*)"lceil", {226, 140, 136, 0}},
{(unsigned char*)"lcub", {123, 0}},
{(unsigned char*)"lcy", {208, 187, 0}},
{(unsigned char*)"ldca", {226, 164, 182, 0}},
{(unsigned char*)"ldquo", {226, 128, 156, 0}},
{(unsigned char*)"ldquor", {226, 128, 158, 0}},
{(unsigned char*)"ldrdhar", {226, 165, 167, 0}},
{(unsigned char*)"ldrushar", {226, 165, 139, 0}},
{(unsigned char*)"ldsh", {226, 134, 178, 0}},
{(unsigned char*)"le", {226, 137, 164, 0}},
{(unsigned char*)"leftarrow", {226, 134, 144, 0}},
{(unsigned char*)"leftarrowtail", {226, 134, 162, 0}},
{(unsigned char*)"leftharpoondown", {226, 134, 189, 0}},
{(unsigned char*)"leftharpoonup", {226, 134, 188, 0}},
{(unsigned char*)"leftleftarrows", {226, 135, 135, 0}},
{(unsigned char*)"leftrightarrow", {226, 134, 148, 0}},
{(unsigned char*)"leftrightarrows", {226, 135, 134, 0}},
{(unsigned char*)"leftrightharpoons", {226, 135, 139, 0}},
{(unsigned char*)"leftrightsquigarrow", {226, 134, 173, 0}},
{(unsigned char*)"leftthreetimes", {226, 139, 139, 0}},
{(unsigned char*)"leg", {226, 139, 154, 0}},
{(unsigned char*)"leq", {226, 137, 164, 0}},
{(unsigned char*)"leqq", {226, 137, 166, 0}},
{(unsigned char*)"leqslant", {226, 169, 189, 0}},
{(unsigned char*)"les", {226, 169, 189, 0}},
{(unsigned char*)"lescc", {226, 170, 168, 0}},
{(unsigned char*)"lesdot", {226, 169, 191, 0}},
{(unsigned char*)"lesdoto", {226, 170, 129, 0}},
{(unsigned char*)"lesdotor", {226, 170, 131, 0}},
{(unsigned char*)"lesg", {226, 139, 154, 239, 184, 128, 0}},
{(unsigned char*)"lesges", {226, 170, 147, 0}},
{(unsigned char*)"lessapprox", {226, 170, 133, 0}},
{(unsigned char*)"lessdot", {226, 139, 150, 0}},
{(unsigned char*)"lesseqgtr", {226, 139, 154, 0}},
{(unsigned char*)"lesseqqgtr", {226, 170, 139, 0}},
{(unsigned char*)"lessgtr", {226, 137, 182, 0}},
{(unsigned char*)"lesssim", {226, 137, 178, 0}},
{(unsigned char*)"lfisht", {226, 165, 188, 0}},
{(unsigned char*)"lfloor", {226, 140, 138, 0}},
{(unsigned char*)"lfr", {240, 157, 148, 169, 0}},
{(unsigned char*)"lg", {226, 137, 182, 0}},
{(unsigned char*)"lgE", {226, 170, 145, 0}},
{(unsigned char*)"lhard", {226, 134, 189, 0}},
{(unsigned char*)"lharu", {226, 134, 188, 0}},
{(unsigned char*)"lharul", {226, 165, 170, 0}},
{(unsigned char*)"lhblk", {226, 150, 132, 0}},
{(unsigned char*)"ljcy", {209, 153, 0}},
{(unsigned char*)"ll", {226, 137, 170, 0}},
{(unsigned char*)"llarr", {226, 135, 135, 0}},
{(unsigned char*)"llcorner", {226, 140, 158, 0}},
{(unsigned char*)"llhard", {226, 165, 171, 0}},
{(unsigned char*)"lltri", {226, 151, 186, 0}},
{(unsigned char*)"lmidot", {197, 128, 0}},
{(unsigned char*)"lmoust", {226, 142, 176, 0}},
{(unsigned char*)"lmoustache", {226, 142, 176, 0}},
{(unsigned char*)"lnE", {226, 137, 168, 0}},
{(unsigned char*)"lnap", {226, 170, 137, 0}},
{(unsigned char*)"lnapprox", {226, 170, 137, 0}},
{(unsigned char*)"lne", {226, 170, 135, 0}},
{(unsigned char*)"lneq", {226, 170, 135, 0}},
{(unsigned char*)"lneqq", {226, 137, 168, 0}},
{(unsigned char*)"lnsim", {226, 139, 166, 0}},
{(unsigned char*)"loang", {226, 159, 172, 0}},
{(unsigned char*)"loarr", {226, 135, 189, 0}},
{(unsigned char*)"lobrk", {226, 159, 166, 0}},
{(unsigned char*)"longleftarrow", {226, 159, 181, 0}},
{(unsigned char*)"longleftrightarrow", {226, 159, 183, 0}},
{(unsigned char*)"longmapsto", {226, 159, 188, 0}},
{(unsigned char*)"longrightarrow", {226, 159, 182, 0}},
{(unsigned char*)"looparrowleft", {226, 134, 171, 0}},
{(unsigned char*)"looparrowright", {226, 134, 172, 0}},
{(unsigned char*)"lopar", {226, 166, 133, 0}},
{(unsigned char*)"lopf", {240, 157, 149, 157, 0}},
{(unsigned char*)"loplus", {226, 168, 173, 0}},
{(unsigned char*)"lotimes", {226, 168, 180, 0}},
{(unsigned char*)"lowast", {226, 136, 151, 0}},
{(unsigned char*)"lowbar", {95, 0}},
{(unsigned char*)"loz", {226, 151, 138, 0}},
{(unsigned char*)"lozenge", {226, 151, 138, 0}},
{(unsigned char*)"lozf", {226, 167, 171, 0}},
{(unsigned char*)"lpar", {40, 0}},
{(unsigned char*)"lparlt", {226, 166, 147, 0}},
{(unsigned char*)"lrarr", {226, 135, 134, 0}},
{(unsigned char*)"lrcorner", {226, 140, 159, 0}},
{(unsigned char*)"lrhar", {226, 135, 139, 0}},
{(unsigned char*)"lrhard", {226, 165, 173, 0}},
{(unsigned char*)"lrm", {226, 128, 142, 0}},
{(unsigned char*)"lrtri", {226, 138, 191, 0}},
{(unsigned char*)"lsaquo", {226, 128, 185, 0}},
{(unsigned char*)"lscr", {240, 157, 147, 129, 0}},
{(unsigned char*)"lsh", {226, 134, 176, 0}},
{(unsigned char*)"lsim", {226, 137, 178, 0}},
{(unsigned char*)"lsime", {226, 170, 141, 0}},
{(unsigned char*)"lsimg", {226, 170, 143, 0}},
{(unsigned char*)"lsqb", {91, 0}},
{(unsigned char*)"lsquo", {226, 128, 152, 0}},
{(unsigned char*)"lsquor", {226, 128, 154, 0}},
{(unsigned char*)"lstrok", {197, 130, 0}},
{(unsigned char*)"lt", {60, 0}},
{(unsigned char*)"ltcc", {226, 170, 166, 0}},
{(unsigned char*)"ltcir", {226, 169, 185, 0}},
{(unsigned char*)"ltdot", {226, 139, 150, 0}},
{(unsigned char*)"lthree", {226, 139, 139, 0}},
{(unsigned char*)"ltimes", {226, 139, 137, 0}},
{(unsigned char*)"ltlarr", {226, 165, 182, 0}},
{(unsigned char*)"ltquest", {226, 169, 187, 0}},
{(unsigned char*)"ltrPar", {226, 166, 150, 0}},
{(unsigned char*)"ltri", {226, 151, 131, 0}},
{(unsigned char*)"ltrie", {226, 138, 180, 0}},
{(unsigned char*)"ltrif", {226, 151, 130, 0}},
{(unsigned char*)"lurdshar", {226, 165, 138, 0}},
{(unsigned char*)"luruhar", {226, 165, 166, 0}},
{(unsigned char*)"lvertneqq", {226, 137, 168, 239, 184, 128, 0}},
{(unsigned char*)"lvnE", {226, 137, 168, 239, 184, 128, 0}},
{(unsigned char*)"mDDot", {226, 136, 186, 0}},
{(unsigned char*)"macr", {194, 175, 0}},
{(unsigned char*)"male", {226, 153, 130, 0}},
{(unsigned char*)"malt", {226, 156, 160, 0}},
{(unsigned char*)"maltese", {226, 156, 160, 0}},
{(unsigned char*)"map", {226, 134, 166, 0}},
{(unsigned char*)"mapsto", {226, 134, 166, 0}},
{(unsigned char*)"mapstodown", {226, 134, 167, 0}},
{(unsigned char*)"mapstoleft", {226, 134, 164, 0}},
{(unsigned char*)"mapstoup", {226, 134, 165, 0}},
{(unsigned char*)"marker", {226, 150, 174, 0}},
{(unsigned char*)"mcomma", {226, 168, 169, 0}},
{(unsigned char*)"mcy", {208, 188, 0}},
{(unsigned char*)"mdash", {226, 128, 148, 0}},
{(unsigned char*)"measuredangle", {226, 136, 161, 0}},
{(unsigned char*)"mfr", {240, 157, 148, 170, 0}},
{(unsigned char*)"mho", {226, 132, 167, 0}},
{(unsigned char*)"micro", {194, 181, 0}},
{(unsigned char*)"mid", {226, 136, 163, 0}},
{(unsigned char*)"midast", {42, 0}},
{(unsigned char*)"midcir", {226, 171, 176, 0}},
{(unsigned char*)"middot", {194, 183, 0}},
{(unsigned char*)"minus", {226, 136, 146, 0}},
{(unsigned char*)"minusb", {226, 138, 159, 0}},
{(unsigned char*)"minusd", {226, 136, 184, 0}},
{(unsigned char*)"minusdu", {226, 168, 170, 0}},
{(unsigned char*)"mlcp", {226, 171, 155, 0}},
{(unsigned char*)"mldr", {226, 128, 166, 0}},
{(unsigned char*)"mnplus", {226, 136, 147, 0}},
{(unsigned char*)"models", {226, 138, 167, 0}},
{(unsigned char*)"mopf", {240, 157, 149, 158, 0}},
{(unsigned char*)"mp", {226, 136, 147, 0}},
{(unsigned char*)"mscr", {240, 157, 147, 130, 0}},
{(unsigned char*)"mstpos", {226, 136, 190, 0}},
{(unsigned char*)"mu", {206, 188, 0}},
{(unsigned char*)"multimap", {226, 138, 184, 0}},
{(unsigned char*)"mumap", {226, 138, 184, 0}},
{(unsigned char*)"nGg", {226, 139, 153, 204, 184, 0}},
{(unsigned char*)"nGt", {226, 137, 171, 226, 131, 146, 0}},
{(unsigned char*)"nGtv", {226, 137, 171, 204, 184, 0}},
{(unsigned char*)"nLeftarrow", {226, 135, 141, 0}},
{(unsigned char*)"nLeftrightarrow", {226, 135, 142, 0}},
{(unsigned char*)"nLl", {226, 139, 152, 204, 184, 0}},
{(unsigned char*)"nLt", {226, 137, 170, 226, 131, 146, 0}},
{(unsigned char*)"nLtv", {226, 137, 170, 204, 184, 0}},
{(unsigned char*)"nRightarrow", {226, 135, 143, 0}},
{(unsigned char*)"nVDash", {226, 138, 175, 0}},
{(unsigned char*)"nVdash", {226, 138, 174, 0}},
{(unsigned char*)"nabla", {226, 136, 135, 0}},
{(unsigned char*)"nacute", {197, 132, 0}},
{(unsigned char*)"nang", {226, 136, 160, 226, 131, 146, 0}},
{(unsigned char*)"nap", {226, 137, 137, 0}},
{(unsigned char*)"napE", {226, 169, 176, 204, 184, 0}},
{(unsigned char*)"napid", {226, 137, 139, 204, 184, 0}},
{(unsigned char*)"napos", {197, 137, 0}},
{(unsigned char*)"napprox", {226, 137, 137, 0}},
{(unsigned char*)"natur", {226, 153, 174, 0}},
{(unsigned char*)"natural", {226, 153, 174, 0}},
{(unsigned char*)"naturals", {226, 132, 149, 0}},
{(unsigned char*)"nbsp", {194, 160, 0}},
{(unsigned char*)"nbump", {226, 137, 142, 204, 184, 0}},
{(unsigned char*)"nbumpe", {226, 137, 143, 204, 184, 0}},
{(unsigned char*)"ncap", {226, 169, 131, 0}},
{(unsigned char*)"ncaron", {197, 136, 0}},
{(unsigned char*)"ncedil", {197, 134, 0}},
{(unsigned char*)"ncong", {226, 137, 135, 0}},
{(unsigned char*)"ncongdot", {226, 169, 173, 204, 184, 0}},
{(unsigned char*)"ncup", {226, 169, 130, 0}},
{(unsigned char*)"ncy", {208, 189, 0}},
{(unsigned char*)"ndash", {226, 128, 147, 0}},
{(unsigned char*)"ne", {226, 137, 160, 0}},
{(unsigned char*)"neArr", {226, 135, 151, 0}},
{(unsigned char*)"nearhk", {226, 164, 164, 0}},
{(unsigned char*)"nearr", {226, 134, 151, 0}},
{(unsigned char*)"nearrow", {226, 134, 151, 0}},
{(unsigned char*)"nedot", {226, 137, 144, 204, 184, 0}},
{(unsigned char*)"nequiv", {226, 137, 162, 0}},
{(unsigned char*)"nesear", {226, 164, 168, 0}},
{(unsigned char*)"nesim", {226, 137, 130, 204, 184, 0}},
{(unsigned char*)"nexist", {226, 136, 132, 0}},
{(unsigned char*)"nexists", {226, 136, 132, 0}},
{(unsigned char*)"nfr", {240, 157, 148, 171, 0}},
{(unsigned char*)"ngE", {226, 137, 167, 204, 184, 0}},
{(unsigned char*)"nge", {226, 137, 177, 0}},
{(unsigned char*)"ngeq", {226, 137, 177, 0}},
{(unsigned char*)"ngeqq", {226, 137, 167, 204, 184, 0}},
{(unsigned char*)"ngeqslant", {226, 169, 190, 204, 184, 0}},
{(unsigned char*)"nges", {226, 169, 190, 204, 184, 0}},
{(unsigned char*)"ngsim", {226, 137, 181, 0}},
{(unsigned char*)"ngt", {226, 137, 175, 0}},
{(unsigned char*)"ngtr", {226, 137, 175, 0}},
{(unsigned char*)"nhArr", {226, 135, 142, 0}},
{(unsigned char*)"nharr", {226, 134, 174, 0}},
{(unsigned char*)"nhpar", {226, 171, 178, 0}},
{(unsigned char*)"ni", {226, 136, 139, 0}},
{(unsigned char*)"nis", {226, 139, 188, 0}},
{(unsigned char*)"nisd", {226, 139, 186, 0}},
{(unsigned char*)"niv", {226, 136, 139, 0}},
{(unsigned char*)"njcy", {209, 154, 0}},
{(unsigned char*)"nlArr", {226, 135, 141, 0}},
{(unsigned char*)"nlE", {226, 137, 166, 204, 184, 0}},
{(unsigned char*)"nlarr", {226, 134, 154, 0}},
{(unsigned char*)"nldr", {226, 128, 165, 0}},
{(unsigned char*)"nle", {226, 137, 176, 0}},
{(unsigned char*)"nleftarrow", {226, 134, 154, 0}},
{(unsigned char*)"nleftrightarrow", {226, 134, 174, 0}},
{(unsigned char*)"nleq", {226, 137, 176, 0}},
{(unsigned char*)"nleqq", {226, 137, 166, 204, 184, 0}},
{(unsigned char*)"nleqslant", {226, 169, 189, 204, 184, 0}},
{(unsigned char*)"nles", {226, 169, 189, 204, 184, 0}},
{(unsigned char*)"nless", {226, 137, 174, 0}},
{(unsigned char*)"nlsim", {226, 137, 180, 0}},
{(unsigned char*)"nlt", {226, 137, 174, 0}},
{(unsigned char*)"nltri", {226, 139, 170, 0}},
{(unsigned char*)"nltrie", {226, 139, 172, 0}},
{(unsigned char*)"nmid", {226, 136, 164, 0}},
{(unsigned char*)"nopf", {240, 157, 149, 159, 0}},
{(unsigned char*)"not", {194, 172, 0}},
{(unsigned char*)"notin", {226, 136, 137, 0}},
{(unsigned char*)"notinE", {226, 139, 185, 204, 184, 0}},
{(unsigned char*)"notindot", {226, 139, 181, 204, 184, 0}},
{(unsigned char*)"notinva", {226, 136, 137, 0}},
{(unsigned char*)"notinvb", {226, 139, 183, 0}},
{(unsigned char*)"notinvc", {226, 139, 182, 0}},
{(unsigned char*)"notni", {226, 136, 140, 0}},
{(unsigned char*)"notniva", {226, 136, 140, 0}},
{(unsigned char*)"notnivb", {226, 139, 190, 0}},
{(unsigned char*)"notnivc", {226, 139, 189, 0}},
{(unsigned char*)"npar", {226, 136, 166, 0}},
{(unsigned char*)"nparallel", {226, 136, 166, 0}},
{(unsigned char*)"nparsl", {226, 171, 189, 226, 131, 165, 0}},
{(unsigned char*)"npart", {226, 136, 130, 204, 184, 0}},
{(unsigned char*)"npolint", {226, 168, 148, 0}},
{(unsigned char*)"npr", {226, 138, 128, 0}},
{(unsigned char*)"nprcue", {226, 139, 160, 0}},
{(unsigned char*)"npre", {226, 170, 175, 204, 184, 0}},
{(unsigned char*)"nprec", {226, 138, 128, 0}},
{(unsigned char*)"npreceq", {226, 170, 175, 204, 184, 0}},
{(unsigned char*)"nrArr", {226, 135, 143, 0}},
{(unsigned char*)"nrarr", {226, 134, 155, 0}},
{(unsigned char*)"nrarrc", {226, 164, 179, 204, 184, 0}},
{(unsigned char*)"nrarrw", {226, 134, 157, 204, 184, 0}},
{(unsigned char*)"nrightarrow", {226, 134, 155, 0}},
{(unsigned char*)"nrtri", {226, 139, 171, 0}},
{(unsigned char*)"nrtrie", {226, 139, 173, 0}},
{(unsigned char*)"nsc", {226, 138, 129, 0}},
{(unsigned char*)"nsccue", {226, 139, 161, 0}},
{(unsigned char*)"nsce", {226, 170, 176, 204, 184, 0}},
{(unsigned char*)"nscr", {240, 157, 147, 131, 0}},
{(unsigned char*)"nshortmid", {226, 136, 164, 0}},
{(unsigned char*)"nshortparallel", {226, 136, 166, 0}},
{(unsigned char*)"nsim", {226, 137, 129, 0}},
{(unsigned char*)"nsime", {226, 137, 132, 0}},
{(unsigned char*)"nsimeq", {226, 137, 132, 0}},
{(unsigned char*)"nsmid", {226, 136, 164, 0}},
{(unsigned char*)"nspar", {226, 136, 166, 0}},
{(unsigned char*)"nsqsube", {226, 139, 162, 0}},
{(unsigned char*)"nsqsupe", {226, 139, 163, 0}},
{(unsigned char*)"nsub", {226, 138, 132, 0}},
{(unsigned char*)"nsubE", {226, 171, 133, 204, 184, 0}},
{(unsigned char*)"nsube", {226, 138, 136, 0}},
{(unsigned char*)"nsubset", {226, 138, 130, 226, 131, 146, 0}},
{(unsigned char*)"nsubseteq", {226, 138, 136, 0}},
{(unsigned char*)"nsubseteqq", {226, 171, 133, 204, 184, 0}},
{(unsigned char*)"nsucc", {226, 138, 129, 0}},
{(unsigned char*)"nsucceq", {226, 170, 176, 204, 184, 0}},
{(unsigned char*)"nsup", {226, 138, 133, 0}},
{(unsigned char*)"nsupE", {226, 171, 134, 204, 184, 0}},
{(unsigned char*)"nsupe", {226, 138, 137, 0}},
{(unsigned char*)"nsupset", {226, 138, 131, 226, 131, 146, 0}},
{(unsigned char*)"nsupseteq", {226, 138, 137, 0}},
{(unsigned char*)"nsupseteqq", {226, 171, 134, 204, 184, 0}},
{(unsigned char*)"ntgl", {226, 137, 185, 0}},
{(unsigned char*)"ntilde", {195, 177, 0}},
{(unsigned char*)"ntlg", {226, 137, 184, 0}},
{(unsigned char*)"ntriangleleft", {226, 139, 170, 0}},
{(unsigned char*)"ntrianglelefteq", {226, 139, 172, 0}},
{(unsigned char*)"ntriangleright", {226, 139, 171, 0}},
{(unsigned char*)"ntrianglerighteq", {226, 139, 173, 0}},
{(unsigned char*)"nu", {206, 189, 0}},
{(unsigned char*)"num", {35, 0}},
{(unsigned char*)"numero", {226, 132, 150, 0}},
{(unsigned char*)"numsp", {226, 128, 135, 0}},
{(unsigned char*)"nvDash", {226, 138, 173, 0}},
{(unsigned char*)"nvHarr", {226, 164, 132, 0}},
{(unsigned char*)"nvap", {226, 137, 141, 226, 131, 146, 0}},
{(unsigned char*)"nvdash", {226, 138, 172, 0}},
{(unsigned char*)"nvge", {226, 137, 165, 226, 131, 146, 0}},
{(unsigned char*)"nvgt", {62, 226, 131, 146, 0}},
{(unsigned char*)"nvinfin", {226, 167, 158, 0}},
{(unsigned char*)"nvlArr", {226, 164, 130, 0}},
{(unsigned char*)"nvle", {226, 137, 164, 226, 131, 146, 0}},
{(unsigned char*)"nvlt", {60, 226, 131, 146, 0}},
{(unsigned char*)"nvltrie", {226, 138, 180, 226, 131, 146, 0}},
{(unsigned char*)"nvrArr", {226, 164, 131, 0}},
{(unsigned char*)"nvrtrie", {226, 138, 181, 226, 131, 146, 0}},
{(unsigned char*)"nvsim", {226, 136, 188, 226, 131, 146, 0}},
{(unsigned char*)"nwArr", {226, 135, 150, 0}},
{(unsigned char*)"nwarhk", {226, 164, 163, 0}},
{(unsigned char*)"nwarr", {226, 134, 150, 0}},
{(unsigned char*)"nwarrow", {226, 134, 150, 0}},
{(unsigned char*)"nwnear", {226, 164, 167, 0}},
{(unsigned char*)"oS", {226, 147, 136, 0}},
{(unsigned char*)"oacute", {195, 179, 0}},
{(unsigned char*)"oast", {226, 138, 155, 0}},
{(unsigned char*)"ocir", {226, 138, 154, 0}},
{(unsigned char*)"ocirc", {195, 180, 0}},
{(unsigned char*)"ocy", {208, 190, 0}},
{(unsigned char*)"odash", {226, 138, 157, 0}},
{(unsigned char*)"odblac", {197, 145, 0}},
{(unsigned char*)"odiv", {226, 168, 184, 0}},
{(unsigned char*)"odot", {226, 138, 153, 0}},
{(unsigned char*)"odsold", {226, 166, 188, 0}},
{(unsigned char*)"oelig", {197, 147, 0}},
{(unsigned char*)"ofcir", {226, 166, 191, 0}},
{(unsigned char*)"ofr", {240, 157, 148, 172, 0}},
{(unsigned char*)"ogon", {203, 155, 0}},
{(unsigned char*)"ograve", {195, 178, 0}},
{(unsigned char*)"ogt", {226, 167, 129, 0}},
{(unsigned char*)"ohbar", {226, 166, 181, 0}},
{(unsigned char*)"ohm", {206, 169, 0}},
{(unsigned char*)"oint", {226, 136, 174, 0}},
{(unsigned char*)"olarr", {226, 134, 186, 0}},
{(unsigned char*)"olcir", {226, 166, 190, 0}},
{(unsigned char*)"olcross", {226, 166, 187, 0}},
{(unsigned char*)"oline", {226, 128, 190, 0}},
{(unsigned char*)"olt", {226, 167, 128, 0}},
{(unsigned char*)"omacr", {197, 141, 0}},
{(unsigned char*)"omega", {207, 137, 0}},
{(unsigned char*)"omicron", {206, 191, 0}},
{(unsigned char*)"omid", {226, 166, 182, 0}},
{(unsigned char*)"ominus", {226, 138, 150, 0}},
{(unsigned char*)"oopf", {240, 157, 149, 160, 0}},
{(unsigned char*)"opar", {226, 166, 183, 0}},
{(unsigned char*)"operp", {226, 166, 185, 0}},
{(unsigned char*)"oplus", {226, 138, 149, 0}},
{(unsigned char*)"or", {226, 136, 168, 0}},
{(unsigned char*)"orarr", {226, 134, 187, 0}},
{(unsigned char*)"ord", {226, 169, 157, 0}},
{(unsigned char*)"order", {226, 132, 180, 0}},
{(unsigned char*)"orderof", {226, 132, 180, 0}},
{(unsigned char*)"ordf", {194, 170, 0}},
{(unsigned char*)"ordm", {194, 186, 0}},
{(unsigned char*)"origof", {226, 138, 182, 0}},
{(unsigned char*)"oror", {226, 169, 150, 0}},
{(unsigned char*)"orslope", {226, 169, 151, 0}},
{(unsigned char*)"orv", {226, 169, 155, 0}},
{(unsigned char*)"oscr", {226, 132, 180, 0}},
{(unsigned char*)"oslash", {195, 184, 0}},
{(unsigned char*)"osol", {226, 138, 152, 0}},
{(unsigned char*)"otilde", {195, 181, 0}},
{(unsigned char*)"otimes", {226, 138, 151, 0}},
{(unsigned char*)"otimesas", {226, 168, 182, 0}},
{(unsigned char*)"ouml", {195, 182, 0}},
{(unsigned char*)"ovbar", {226, 140, 189, 0}},
{(unsigned char*)"par", {226, 136, 165, 0}},
{(unsigned char*)"para", {194, 182, 0}},
{(unsigned char*)"parallel", {226, 136, 165, 0}},
{(unsigned char*)"parsim", {226, 171, 179, 0}},
{(unsigned char*)"parsl", {226, 171, 189, 0}},
{(unsigned char*)"part", {226, 136, 130, 0}},
{(unsigned char*)"pcy", {208, 191, 0}},
{(unsigned char*)"percnt", {37, 0}},
{(unsigned char*)"period", {46, 0}},
{(unsigned char*)"permil", {226, 128, 176, 0}},
{(unsigned char*)"perp", {226, 138, 165, 0}},
{(unsigned char*)"pertenk", {226, 128, 177, 0}},
{(unsigned char*)"pfr", {240, 157, 148, 173, 0}},
{(unsigned char*)"phi", {207, 134, 0}},
{(unsigned char*)"phiv", {207, 149, 0}},
{(unsigned char*)"phmmat", {226, 132, 179, 0}},
{(unsigned char*)"phone", {226, 152, 142, 0}},
{(unsigned char*)"pi", {207, 128, 0}},
{(unsigned char*)"pitchfork", {226, 139, 148, 0}},
{(unsigned char*)"piv", {207, 150, 0}},
{(unsigned char*)"planck", {226, 132, 143, 0}},
{(unsigned char*)"planckh", {226, 132, 142, 0}},
{(unsigned char*)"plankv", {226, 132, 143, 0}},
{(unsigned char*)"plus", {43, 0}},
{(unsigned char*)"plusacir", {226, 168, 163, 0}},
{(unsigned char*)"plusb", {226, 138, 158, 0}},
{(unsigned char*)"pluscir", {226, 168, 162, 0}},
{(unsigned char*)"plusdo", {226, 136, 148, 0}},
{(unsigned char*)"plusdu", {226, 168, 165, 0}},
{(unsigned char*)"pluse", {226, 169, 178, 0}},
{(unsigned char*)"plusmn", {194, 177, 0}},
{(unsigned char*)"plussim", {226, 168, 166, 0}},
{(unsigned char*)"plustwo", {226, 168, 167, 0}},
{(unsigned char*)"pm", {194, 177, 0}},
{(unsigned char*)"pointint", {226, 168, 149, 0}},
{(unsigned char*)"popf", {240, 157, 149, 161, 0}},
{(unsigned char*)"pound", {194, 163, 0}},
{(unsigned char*)"pr", {226, 137, 186, 0}},
{(unsigned char*)"prE", {226, 170, 179, 0}},
{(unsigned char*)"prap", {226, 170, 183, 0}},
{(unsigned char*)"prcue", {226, 137, 188, 0}},
{(unsigned char*)"pre", {226, 170, 175, 0}},
{(unsigned char*)"prec", {226, 137, 186, 0}},
{(unsigned char*)"precapprox", {226, 170, 183, 0}},
{(unsigned char*)"preccurlyeq", {226, 137, 188, 0}},
{(unsigned char*)"preceq", {226, 170, 175, 0}},
{(unsigned char*)"precnapprox", {226, 170, 185, 0}},
{(unsigned char*)"precneqq", {226, 170, 181, 0}},
{(unsigned char*)"precnsim", {226, 139, 168, 0}},
{(unsigned char*)"precsim", {226, 137, 190, 0}},
{(unsigned char*)"prime", {226, 128, 178, 0}},
{(unsigned char*)"primes", {226, 132, 153, 0}},
{(unsigned char*)"prnE", {226, 170, 181, 0}},
{(unsigned char*)"prnap", {226, 170, 185, 0}},
{(unsigned char*)"prnsim", {226, 139, 168, 0}},
{(unsigned char*)"prod", {226, 136, 143, 0}},
{(unsigned char*)"profalar", {226, 140, 174, 0}},
{(unsigned char*)"profline", {226, 140, 146, 0}},
{(unsigned char*)"profsurf", {226, 140, 147, 0}},
{(unsigned char*)"prop", {226, 136, 157, 0}},
{(unsigned char*)"propto", {226, 136, 157, 0}},
{(unsigned char*)"prsim", {226, 137, 190, 0}},
{(unsigned char*)"prurel", {226, 138, 176, 0}},
{(unsigned char*)"pscr", {240, 157, 147, 133, 0}},
{(unsigned char*)"psi", {207, 136, 0}},
{(unsigned char*)"puncsp", {226, 128, 136, 0}},
{(unsigned char*)"qfr", {240, 157, 148, 174, 0}},
{(unsigned char*)"qint", {226, 168, 140, 0}},
{(unsigned char*)"qopf", {240, 157, 149, 162, 0}},
{(unsigned char*)"qprime", {226, 129, 151, 0}},
{(unsigned char*)"qscr", {240, 157, 147, 134, 0}},
{(unsigned char*)"quaternions", {226, 132, 141, 0}},
{(unsigned char*)"quatint", {226, 168, 150, 0}},
{(unsigned char*)"quest", {63, 0}},
{(unsigned char*)"questeq", {226, 137, 159, 0}},
{(unsigned char*)"quot", {34, 0}},
{(unsigned char*)"rAarr", {226, 135, 155, 0}},
{(unsigned char*)"rArr", {226, 135, 146, 0}},
{(unsigned char*)"rAtail", {226, 164, 156, 0}},
{(unsigned char*)"rBarr", {226, 164, 143, 0}},
{(unsigned char*)"rHar", {226, 165, 164, 0}},
{(unsigned char*)"race", {226, 136, 189, 204, 177, 0}},
{(unsigned char*)"racute", {197, 149, 0}},
{(unsigned char*)"radic", {226, 136, 154, 0}},
{(unsigned char*)"raemptyv", {226, 166, 179, 0}},
{(unsigned char*)"rang", {226, 159, 169, 0}},
{(unsigned char*)"rangd", {226, 166, 146, 0}},
{(unsigned char*)"range", {226, 166, 165, 0}},
{(unsigned char*)"rangle", {226, 159, 169, 0}},
{(unsigned char*)"raquo", {194, 187, 0}},
{(unsigned char*)"rarr", {226, 134, 146, 0}},
{(unsigned char*)"rarrap", {226, 165, 181, 0}},
{(unsigned char*)"rarrb", {226, 135, 165, 0}},
{(unsigned char*)"rarrbfs", {226, 164, 160, 0}},
{(unsigned char*)"rarrc", {226, 164, 179, 0}},
{(unsigned char*)"rarrfs", {226, 164, 158, 0}},
{(unsigned char*)"rarrhk", {226, 134, 170, 0}},
{(unsigned char*)"rarrlp", {226, 134, 172, 0}},
{(unsigned char*)"rarrpl", {226, 165, 133, 0}},
{(unsigned char*)"rarrsim", {226, 165, 180, 0}},
{(unsigned char*)"rarrtl", {226, 134, 163, 0}},
{(unsigned char*)"rarrw", {226, 134, 157, 0}},
{(unsigned char*)"ratail", {226, 164, 154, 0}},
{(unsigned char*)"ratio", {226, 136, 182, 0}},
{(unsigned char*)"rationals", {226, 132, 154, 0}},
{(unsigned char*)"rbarr", {226, 164, 141, 0}},
{(unsigned char*)"rbbrk", {226, 157, 179, 0}},
{(unsigned char*)"rbrace", {125, 0}},
{(unsigned char*)"rbrack", {93, 0}},
{(unsigned char*)"rbrke", {226, 166, 140, 0}},
{(unsigned char*)"rbrksld", {226, 166, 142, 0}},
{(unsigned char*)"rbrkslu", {226, 166, 144, 0}},
{(unsigned char*)"rcaron", {197, 153, 0}},
{(unsigned char*)"rcedil", {197, 151, 0}},
{(unsigned char*)"rceil", {226, 140, 137, 0}},
{(unsigned char*)"rcub", {125, 0}},
{(unsigned char*)"rcy", {209, 128, 0}},
{(unsigned char*)"rdca", {226, 164, 183, 0}},
{(unsigned char*)"rdldhar", {226, 165, 169, 0}},
{(unsigned char*)"rdquo", {226, 128, 157, 0}},
{(unsigned char*)"rdquor", {226, 128, 157, 0}},
{(unsigned char*)"rdsh", {226, 134, 179, 0}},
{(unsigned char*)"real", {226, 132, 156, 0}},
{(unsigned char*)"realine", {226, 132, 155, 0}},
{(unsigned char*)"realpart", {226, 132, 156, 0}},
{(unsigned char*)"reals", {226, 132, 157, 0}},
{(unsigned char*)"rect", {226, 150, 173, 0}},
{(unsigned char*)"reg", {194, 174, 0}},
{(unsigned char*)"rfisht", {226, 165, 189, 0}},
{(unsigned char*)"rfloor", {226, 140, 139, 0}},
{(unsigned char*)"rfr", {240, 157, 148, 175, 0}},
{(unsigned char*)"rhard", {226, 135, 129, 0}},
{(unsigned char*)"rharu", {226, 135, 128, 0}},
{(unsigned char*)"rharul", {226, 165, 172, 0}},
{(unsigned char*)"rho", {207, 129, 0}},
{(unsigned char*)"rhov", {207, 177, 0}},
{(unsigned char*)"rightarrow", {226, 134, 146, 0}},
{(unsigned char*)"rightarrowtail", {226, 134, 163, 0}},
{(unsigned char*)"rightharpoondown", {226, 135, 129, 0}},
{(unsigned char*)"rightharpoonup", {226, 135, 128, 0}},
{(unsigned char*)"rightleftarrows", {226, 135, 132, 0}},
{(unsigned char*)"rightleftharpoons", {226, 135, 140, 0}},
{(unsigned char*)"rightrightarrows", {226, 135, 137, 0}},
{(unsigned char*)"rightsquigarrow", {226, 134, 157, 0}},
{(unsigned char*)"rightthreetimes", {226, 139, 140, 0}},
{(unsigned char*)"ring", {203, 154, 0}},
{(unsigned char*)"risingdotseq", {226, 137, 147, 0}},
{(unsigned char*)"rlarr", {226, 135, 132, 0}},
{(unsigned char*)"rlhar", {226, 135, 140, 0}},
{(unsigned char*)"rlm", {226, 128, 143, 0}},
{(unsigned char*)"rmoust", {226, 142, 177, 0}},
{(unsigned char*)"rmoustache", {226, 142, 177, 0}},
{(unsigned char*)"rnmid", {226, 171, 174, 0}},
{(unsigned char*)"roang", {226, 159, 173, 0}},
{(unsigned char*)"roarr", {226, 135, 190, 0}},
{(unsigned char*)"robrk", {226, 159, 167, 0}},
{(unsigned char*)"ropar", {226, 166, 134, 0}},
{(unsigned char*)"ropf", {240, 157, 149, 163, 0}},
{(unsigned char*)"roplus", {226, 168, 174, 0}},
{(unsigned char*)"rotimes", {226, 168, 181, 0}},
{(unsigned char*)"rpar", {41, 0}},
{(unsigned char*)"rpargt", {226, 166, 148, 0}},
{(unsigned char*)"rppolint", {226, 168, 146, 0}},
{(unsigned char*)"rrarr", {226, 135, 137, 0}},
{(unsigned char*)"rsaquo", {226, 128, 186, 0}},
{(unsigned char*)"rscr", {240, 157, 147, 135, 0}},
{(unsigned char*)"rsh", {226, 134, 177, 0}},
{(unsigned char*)"rsqb", {93, 0}},
{(unsigned char*)"rsquo", {226, 128, 153, 0}},
{(unsigned char*)"rsquor", {226, 128, 153, 0}},
{(unsigned char*)"rthree", {226, 139, 140, 0}},
{(unsigned char*)"rtimes", {226, 139, 138, 0}},
{(unsigned char*)"rtri", {226, 150, 185, 0}},
{(unsigned char*)"rtrie", {226, 138, 181, 0}},
{(unsigned char*)"rtrif", {226, 150, 184, 0}},
{(unsigned char*)"rtriltri", {226, 167, 142, 0}},
{(unsigned char*)"ruluhar", {226, 165, 168, 0}},
{(unsigned char*)"rx", {226, 132, 158, 0}},
{(unsigned char*)"sacute", {197, 155, 0}},
{(unsigned char*)"sbquo", {226, 128, 154, 0}},
{(unsigned char*)"sc", {226, 137, 187, 0}},
{(unsigned char*)"scE", {226, 170, 180, 0}},
{(unsigned char*)"scap", {226, 170, 184, 0}},
{(unsigned char*)"scaron", {197, 161, 0}},
{(unsigned char*)"sccue", {226, 137, 189, 0}},
{(unsigned char*)"sce", {226, 170, 176, 0}},
{(unsigned char*)"scedil", {197, 159, 0}},
{(unsigned char*)"scirc", {197, 157, 0}},
{(unsigned char*)"scnE", {226, 170, 182, 0}},
{(unsigned char*)"scnap", {226, 170, 186, 0}},
{(unsigned char*)"scnsim", {226, 139, 169, 0}},
{(unsigned char*)"scpolint", {226, 168, 147, 0}},
{(unsigned char*)"scsim", {226, 137, 191, 0}},
{(unsigned char*)"scy", {209, 129, 0}},
{(unsigned char*)"sdot", {226, 139, 133, 0}},
{(unsigned char*)"sdotb", {226, 138, 161, 0}},
{(unsigned char*)"sdote", {226, 169, 166, 0}},
{(unsigned char*)"seArr", {226, 135, 152, 0}},
{(unsigned char*)"searhk", {226, 164, 165, 0}},
{(unsigned char*)"searr", {226, 134, 152, 0}},
{(unsigned char*)"searrow", {226, 134, 152, 0}},
{(unsigned char*)"sect", {194, 167, 0}},
{(unsigned char*)"semi", {59, 0}},
{(unsigned char*)"seswar", {226, 164, 169, 0}},
{(unsigned char*)"setminus", {226, 136, 150, 0}},
{(unsigned char*)"setmn", {226, 136, 150, 0}},
{(unsigned char*)"sext", {226, 156, 182, 0}},
{(unsigned char*)"sfr", {240, 157, 148, 176, 0}},
{(unsigned char*)"sfrown", {226, 140, 162, 0}},
{(unsigned char*)"sharp", {226, 153, 175, 0}},
{(unsigned char*)"shchcy", {209, 137, 0}},
{(unsigned char*)"shcy", {209, 136, 0}},
{(unsigned char*)"shortmid", {226, 136, 163, 0}},
{(unsigned char*)"shortparallel", {226, 136, 165, 0}},
{(unsigned char*)"shy", {194, 173, 0}},
{(unsigned char*)"sigma", {207, 131, 0}},
{(unsigned char*)"sigmaf", {207, 130, 0}},
{(unsigned char*)"sigmav", {207, 130, 0}},
{(unsigned char*)"sim", {226, 136, 188, 0}},
{(unsigned char*)"simdot", {226, 169, 170, 0}},
{(unsigned char*)"sime", {226, 137, 131, 0}},
{(unsigned char*)"simeq", {226, 137, 131, 0}},
{(unsigned char*)"simg", {226, 170, 158, 0}},
{(unsigned char*)"simgE", {226, 170, 160, 0}},
{(unsigned char*)"siml", {226, 170, 157, 0}},
{(unsigned char*)"simlE", {226, 170, 159, 0}},
{(unsigned char*)"simne", {226, 137, 134, 0}},
{(unsigned char*)"simplus", {226, 168, 164, 0}},
{(unsigned char*)"simrarr", {226, 165, 178, 0}},
{(unsigned char*)"slarr", {226, 134, 144, 0}},
{(unsigned char*)"smallsetminus", {226, 136, 150, 0}},
{(unsigned char*)"smashp", {226, 168, 179, 0}},
{(unsigned char*)"smeparsl", {226, 167, 164, 0}},
{(unsigned char*)"smid", {226, 136, 163, 0}},
{(unsigned char*)"smile", {226, 140, 163, 0}},
{(unsigned char*)"smt", {226, 170, 170, 0}},
{(unsigned char*)"smte", {226, 170, 172, 0}},
{(unsigned char*)"smtes", {226, 170, 172, 239, 184, 128, 0}},
{(unsigned char*)"softcy", {209, 140, 0}},
{(unsigned char*)"sol", {47, 0}},
{(unsigned char*)"solb", {226, 167, 132, 0}},
{(unsigned char*)"solbar", {226, 140, 191, 0}},
{(unsigned char*)"sopf", {240, 157, 149, 164, 0}},
{(unsigned char*)"spades", {226, 153, 160, 0}},
{(unsigned char*)"spadesuit", {226, 153, 160, 0}},
{(unsigned char*)"spar", {226, 136, 165, 0}},
{(unsigned char*)"sqcap", {226, 138, 147, 0}},
{(unsigned char*)"sqcaps", {226, 138, 147, 239, 184, 128, 0}},
{(unsigned char*)"sqcup", {226, 138, 148, 0}},
{(unsigned char*)"sqcups", {226, 138, 148, 239, 184, 128, 0}},
{(unsigned char*)"sqsub", {226, 138, 143, 0}},
{(unsigned char*)"sqsube", {226, 138, 145, 0}},
{(unsigned char*)"sqsubset", {226, 138, 143, 0}},
{(unsigned char*)"sqsubseteq", {226, 138, 145, 0}},
{(unsigned char*)"sqsup", {226, 138, 144, 0}},
{(unsigned char*)"sqsupe", {226, 138, 146, 0}},
{(unsigned char*)"sqsupset", {226, 138, 144, 0}},
{(unsigned char*)"sqsupseteq", {226, 138, 146, 0}},
{(unsigned char*)"squ", {226, 150, 161, 0}},
{(unsigned char*)"square", {226, 150, 161, 0}},
{(unsigned char*)"squarf", {226, 150, 170, 0}},
{(unsigned char*)"squf", {226, 150, 170, 0}},
{(unsigned char*)"srarr", {226, 134, 146, 0}},
{(unsigned char*)"sscr", {240, 157, 147, 136, 0}},
{(unsigned char*)"ssetmn", {226, 136, 150, 0}},
{(unsigned char*)"ssmile", {226, 140, 163, 0}},
{(unsigned char*)"sstarf", {226, 139, 134, 0}},
{(unsigned char*)"star", {226, 152, 134, 0}},
{(unsigned char*)"starf", {226, 152, 133, 0}},
{(unsigned char*)"straightepsilon", {207, 181, 0}},
{(unsigned char*)"straightphi", {207, 149, 0}},
{(unsigned char*)"strns", {194, 175, 0}},
{(unsigned char*)"sub", {226, 138, 130, 0}},
{(unsigned char*)"subE", {226, 171, 133, 0}},
{(unsigned char*)"subdot", {226, 170, 189, 0}},
{(unsigned char*)"sube", {226, 138, 134, 0}},
{(unsigned char*)"subedot", {226, 171, 131, 0}},
{(unsigned char*)"submult", {226, 171, 129, 0}},
{(unsigned char*)"subnE", {226, 171, 139, 0}},
{(unsigned char*)"subne", {226, 138, 138, 0}},
{(unsigned char*)"subplus", {226, 170, 191, 0}},
{(unsigned char*)"subrarr", {226, 165, 185, 0}},
{(unsigned char*)"subset", {226, 138, 130, 0}},
{(unsigned char*)"subseteq", {226, 138, 134, 0}},
{(unsigned char*)"subseteqq", {226, 171, 133, 0}},
{(unsigned char*)"subsetneq", {226, 138, 138, 0}},
{(unsigned char*)"subsetneqq", {226, 171, 139, 0}},
{(unsigned char*)"subsim", {226, 171, 135, 0}},
{(unsigned char*)"subsub", {226, 171, 149, 0}},
{(unsigned char*)"subsup", {226, 171, 147, 0}},
{(unsigned char*)"succ", {226, 137, 187, 0}},
{(unsigned char*)"succapprox", {226, 170, 184, 0}},
{(unsigned char*)"succcurlyeq", {226, 137, 189, 0}},
{(unsigned char*)"succeq", {226, 170, 176, 0}},
{(unsigned char*)"succnapprox", {226, 170, 186, 0}},
{(unsigned char*)"succneqq", {226, 170, 182, 0}},
{(unsigned char*)"succnsim", {226, 139, 169, 0}},
{(unsigned char*)"succsim", {226, 137, 191, 0}},
{(unsigned char*)"sum", {226, 136, 145, 0}},
{(unsigned char*)"sung", {226, 153, 170, 0}},
{(unsigned char*)"sup", {226, 138, 131, 0}},
{(unsigned char*)"sup1", {194, 185, 0}},
{(unsigned char*)"sup2", {194, 178, 0}},
{(unsigned char*)"sup3", {194, 179, 0}},
{(unsigned char*)"supE", {226, 171, 134, 0}},
{(unsigned char*)"supdot", {226, 170, 190, 0}},
{(unsigned char*)"supdsub", {226, 171, 152, 0}},
{(unsigned char*)"supe", {226, 138, 135, 0}},
{(unsigned char*)"supedot", {226, 171, 132, 0}},
{(unsigned char*)"suphsol", {226, 159, 137, 0}},
{(unsigned char*)"suphsub", {226, 171, 151, 0}},
{(unsigned char*)"suplarr", {226, 165, 187, 0}},
{(unsigned char*)"supmult", {226, 171, 130, 0}},
{(unsigned char*)"supnE", {226, 171, 140, 0}},
{(unsigned char*)"supne", {226, 138, 139, 0}},
{(unsigned char*)"supplus", {226, 171, 128, 0}},
{(unsigned char*)"supset", {226, 138, 131, 0}},
{(unsigned char*)"supseteq", {226, 138, 135, 0}},
{(unsigned char*)"supseteqq", {226, 171, 134, 0}},
{(unsigned char*)"supsetneq", {226, 138, 139, 0}},
{(unsigned char*)"supsetneqq", {226, 171, 140, 0}},
{(unsigned char*)"supsim", {226, 171, 136, 0}},
{(unsigned char*)"supsub", {226, 171, 148, 0}},
{(unsigned char*)"supsup", {226, 171, 150, 0}},
{(unsigned char*)"swArr", {226, 135, 153, 0}},
{(unsigned char*)"swarhk", {226, 164, 166, 0}},
{(unsigned char*)"swarr", {226, 134, 153, 0}},
{(unsigned char*)"swarrow", {226, 134, 153, 0}},
{(unsigned char*)"swnwar", {226, 164, 170, 0}},
{(unsigned char*)"szlig", {195, 159, 0}},
{(unsigned char*)"target", {226, 140, 150, 0}},
{(unsigned char*)"tau", {207, 132, 0}},
{(unsigned char*)"tbrk", {226, 142, 180, 0}},
{(unsigned char*)"tcaron", {197, 165, 0}},
{(unsigned char*)"tcedil", {197, 163, 0}},
{(unsigned char*)"tcy", {209, 130, 0}},
{(unsigned char*)"tdot", {226, 131, 155, 0}},
{(unsigned char*)"telrec", {226, 140, 149, 0}},
{(unsigned char*)"tfr", {240, 157, 148, 177, 0}},
{(unsigned char*)"there4", {226, 136, 180, 0}},
{(unsigned char*)"therefore", {226, 136, 180, 0}},
{(unsigned char*)"theta", {206, 184, 0}},
{(unsigned char*)"thetasym", {207, 145, 0}},
{(unsigned char*)"thetav", {207, 145, 0}},
{(unsigned char*)"thickapprox", {226, 137, 136, 0}},
{(unsigned char*)"thicksim", {226, 136, 188, 0}},
{(unsigned char*)"thinsp", {226, 128, 137, 0}},
{(unsigned char*)"thkap", {226, 137, 136, 0}},
{(unsigned char*)"thksim", {226, 136, 188, 0}},
{(unsigned char*)"thorn", {195, 190, 0}},
{(unsigned char*)"tilde", {203, 156, 0}},
{(unsigned char*)"times", {195, 151, 0}},
{(unsigned char*)"timesb", {226, 138, 160, 0}},
{(unsigned char*)"timesbar", {226, 168, 177, 0}},
{(unsigned char*)"timesd", {226, 168, 176, 0}},
{(unsigned char*)"tint", {226, 136, 173, 0}},
{(unsigned char*)"toea", {226, 164, 168, 0}},
{(unsigned char*)"top", {226, 138, 164, 0}},
{(unsigned char*)"topbot", {226, 140, 182, 0}},
{(unsigned char*)"topcir", {226, 171, 177, 0}},
{(unsigned char*)"topf", {240, 157, 149, 165, 0}},
{(unsigned char*)"topfork", {226, 171, 154, 0}},
{(unsigned char*)"tosa", {226, 164, 169, 0}},
{(unsigned char*)"tprime", {226, 128, 180, 0}},
{(unsigned char*)"trade", {226, 132, 162, 0}},
{(unsigned char*)"triangle", {226, 150, 181, 0}},
{(unsigned char*)"triangledown", {226, 150, 191, 0}},
{(unsigned char*)"triangleleft", {226, 151, 131, 0}},
{(unsigned char*)"trianglelefteq", {226, 138, 180, 0}},
{(unsigned char*)"triangleq", {226, 137, 156, 0}},
{(unsigned char*)"triangleright", {226, 150, 185, 0}},
{(unsigned char*)"trianglerighteq", {226, 138, 181, 0}},
{(unsigned char*)"tridot", {226, 151, 172, 0}},
{(unsigned char*)"trie", {226, 137, 156, 0}},
{(unsigned char*)"triminus", {226, 168, 186, 0}},
{(unsigned char*)"triplus", {226, 168, 185, 0}},
{(unsigned char*)"trisb", {226, 167, 141, 0}},
{(unsigned char*)"tritime", {226, 168, 187, 0}},
{(unsigned char*)"trpezium", {226, 143, 162, 0}},
{(unsigned char*)"tscr", {240, 157, 147, 137, 0}},
{(unsigned char*)"tscy", {209, 134, 0}},
{(unsigned char*)"tshcy", {209, 155, 0}},
{(unsigned char*)"tstrok", {197, 167, 0}},
{(unsigned char*)"twixt", {226, 137, 172, 0}},
{(unsigned char*)"twoheadleftarrow", {226, 134, 158, 0}},
{(unsigned char*)"twoheadrightarrow", {226, 134, 160, 0}},
{(unsigned char*)"uArr", {226, 135, 145, 0}},
{(unsigned char*)"uHar", {226, 165, 163, 0}},
{(unsigned char*)"uacute", {195, 186, 0}},
{(unsigned char*)"uarr", {226, 134, 145, 0}},
{(unsigned char*)"ubrcy", {209, 158, 0}},
{(unsigned char*)"ubreve", {197, 173, 0}},
{(unsigned char*)"ucirc", {195, 187, 0}},
{(unsigned char*)"ucy", {209, 131, 0}},
{(unsigned char*)"udarr", {226, 135, 133, 0}},
{(unsigned char*)"udblac", {197, 177, 0}},
{(unsigned char*)"udhar", {226, 165, 174, 0}},
{(unsigned char*)"ufisht", {226, 165, 190, 0}},
{(unsigned char*)"ufr", {240, 157, 148, 178, 0}},
{(unsigned char*)"ugrave", {195, 185, 0}},
{(unsigned char*)"uharl", {226, 134, 191, 0}},
{(unsigned char*)"uharr", {226, 134, 190, 0}},
{(unsigned char*)"uhblk", {226, 150, 128, 0}},
{(unsigned char*)"ulcorn", {226, 140, 156, 0}},
{(unsigned char*)"ulcorner", {226, 140, 156, 0}},
{(unsigned char*)"ulcrop", {226, 140, 143, 0}},
{(unsigned char*)"ultri", {226, 151, 184, 0}},
{(unsigned char*)"umacr", {197, 171, 0}},
{(unsigned char*)"uml", {194, 168, 0}},
{(unsigned char*)"uogon", {197, 179, 0}},
{(unsigned char*)"uopf", {240, 157, 149, 166, 0}},
{(unsigned char*)"uparrow", {226, 134, 145, 0}},
{(unsigned char*)"updownarrow", {226, 134, 149, 0}},
{(unsigned char*)"upharpoonleft", {226, 134, 191, 0}},
{(unsigned char*)"upharpoonright", {226, 134, 190, 0}},
{(unsigned char*)"uplus", {226, 138, 142, 0}},
{(unsigned char*)"upsi", {207, 133, 0}},
{(unsigned char*)"upsih", {207, 146, 0}},
{(unsigned char*)"upsilon", {207, 133, 0}},
{(unsigned char*)"upuparrows", {226, 135, 136, 0}},
{(unsigned char*)"urcorn", {226, 140, 157, 0}},
{(unsigned char*)"urcorner", {226, 140, 157, 0}},
{(unsigned char*)"urcrop", {226, 140, 142, 0}},
{(unsigned char*)"uring", {197, 175, 0}},
{(unsigned char*)"urtri", {226, 151, 185, 0}},
{(unsigned char*)"uscr", {240, 157, 147, 138, 0}},
{(unsigned char*)"utdot", {226, 139, 176, 0}},
{(unsigned char*)"utilde", {197, 169, 0}},
{(unsigned char*)"utri", {226, 150, 181, 0}},
{(unsigned char*)"utrif", {226, 150, 180, 0}},
{(unsigned char*)"uuarr", {226, 135, 136, 0}},
{(unsigned char*)"uuml", {195, 188, 0}},
{(unsigned char*)"uwangle", {226, 166, 167, 0}},
{(unsigned char*)"vArr", {226, 135, 149, 0}},
{(unsigned char*)"vBar", {226, 171, 168, 0}},
{(unsigned char*)"vBarv", {226, 171, 169, 0}},
{(unsigned char*)"vDash", {226, 138, 168, 0}},
{(unsigned char*)"vangrt", {226, 166, 156, 0}},
{(unsigned char*)"varepsilon", {207, 181, 0}},
{(unsigned char*)"varkappa", {207, 176, 0}},
{(unsigned char*)"varnothing", {226, 136, 133, 0}},
{(unsigned char*)"varphi", {207, 149, 0}},
{(unsigned char*)"varpi", {207, 150, 0}},
{(unsigned char*)"varpropto", {226, 136, 157, 0}},
{(unsigned char*)"varr", {226, 134, 149, 0}},
{(unsigned char*)"varrho", {207, 177, 0}},
{(unsigned char*)"varsigma", {207, 130, 0}},
{(unsigned char*)"varsubsetneq", {226, 138, 138, 239, 184, 128, 0}},
{(unsigned char*)"varsubsetneqq", {226, 171, 139, 239, 184, 128, 0}},
{(unsigned char*)"varsupsetneq", {226, 138, 139, 239, 184, 128, 0}},
{(unsigned char*)"varsupsetneqq", {226, 171, 140, 239, 184, 128, 0}},
{(unsigned char*)"vartheta", {207, 145, 0}},
{(unsigned char*)"vartriangleleft", {226, 138, 178, 0}},
{(unsigned char*)"vartriangleright", {226, 138, 179, 0}},
{(unsigned char*)"vcy", {208, 178, 0}},
{(unsigned char*)"vdash", {226, 138, 162, 0}},
{(unsigned char*)"vee", {226, 136, 168, 0}},
{(unsigned char*)"veebar", {226, 138, 187, 0}},
{(unsigned char*)"veeeq", {226, 137, 154, 0}},
{(unsigned char*)"vellip", {226, 139, 174, 0}},
{(unsigned char*)"verbar", {124, 0}},
{(unsigned char*)"vert", {124, 0}},
{(unsigned char*)"vfr", {240, 157, 148, 179, 0}},
{(unsigned char*)"vltri", {226, 138, 178, 0}},
{(unsigned char*)"vnsub", {226, 138, 130, 226, 131, 146, 0}},
{(unsigned char*)"vnsup", {226, 138, 131, 226, 131, 146, 0}},
{(unsigned char*)"vopf", {240, 157, 149, 167, 0}},
{(unsigned char*)"vprop", {226, 136, 157, 0}},
{(unsigned char*)"vrtri", {226, 138, 179, 0}},
{(unsigned char*)"vscr", {240, 157, 147, 139, 0}},
{(unsigned char*)"vsubnE", {226, 171, 139, 239, 184, 128, 0}},
{(unsigned char*)"vsubne", {226, 138, 138, 239, 184, 128, 0}},
{(unsigned char*)"vsupnE", {226, 171, 140, 239, 184, 128, 0}},
{(unsigned char*)"vsupne", {226, 138, 139, 239, 184, 128, 0}},
{(unsigned char*)"vzigzag", {226, 166, 154, 0}},
{(unsigned char*)"wcirc", {197, 181, 0}},
{(unsigned char*)"wedbar", {226, 169, 159, 0}},
{(unsigned char*)"wedge", {226, 136, 167, 0}},
{(unsigned char*)"wedgeq", {226, 137, 153, 0}},
{(unsigned char*)"weierp", {226, 132, 152, 0}},
{(unsigned char*)"wfr", {240, 157, 148, 180, 0}},
{(unsigned char*)"wopf", {240, 157, 149, 168, 0}},
{(unsigned char*)"wp", {226, 132, 152, 0}},
{(unsigned char*)"wr", {226, 137, 128, 0}},
{(unsigned char*)"wreath", {226, 137, 128, 0}},
{(unsigned char*)"wscr", {240, 157, 147, 140, 0}},
{(unsigned char*)"xcap", {226, 139, 130, 0}},
{(unsigned char*)"xcirc", {226, 151, 175, 0}},
{(unsigned char*)"xcup", {226, 139, 131, 0}},
{(unsigned char*)"xdtri", {226, 150, 189, 0}},
{(unsigned char*)"xfr", {240, 157, 148, 181, 0}},
{(unsigned char*)"xhArr", {226, 159, 186, 0}},
{(unsigned char*)"xharr", {226, 159, 183, 0}},
{(unsigned char*)"xi", {206, 190, 0}},
{(unsigned char*)"xlArr", {226, 159, 184, 0}},
{(unsigned char*)"xlarr", {226, 159, 181, 0}},
{(unsigned char*)"xmap", {226, 159, 188, 0}},
{(unsigned char*)"xnis", {226, 139, 187, 0}},
{(unsigned char*)"xodot", {226, 168, 128, 0}},
{(unsigned char*)"xopf", {240, 157, 149, 169, 0}},
{(unsigned char*)"xoplus", {226, 168, 129, 0}},
{(unsigned char*)"xotime", {226, 168, 130, 0}},
{(unsigned char*)"xrArr", {226, 159, 185, 0}},
{(unsigned char*)"xrarr", {226, 159, 182, 0}},
{(unsigned char*)"xscr", {240, 157, 147, 141, 0}},
{(unsigned char*)"xsqcup", {226, 168, 134, 0}},
{(unsigned char*)"xuplus", {226, 168, 132, 0}},
{(unsigned char*)"xutri", {226, 150, 179, 0}},
{(unsigned char*)"xvee", {226, 139, 129, 0}},
{(unsigned char*)"xwedge", {226, 139, 128, 0}},
{(unsigned char*)"yacute", {195, 189, 0}},
{(unsigned char*)"yacy", {209, 143, 0}},
{(unsigned char*)"ycirc", {197, 183, 0}},
{(unsigned char*)"ycy", {209, 139, 0}},
{(unsigned char*)"yen", {194, 165, 0}},
{(unsigned char*)"yfr", {240, 157, 148, 182, 0}},
{(unsigned char*)"yicy", {209, 151, 0}},
{(unsigned char*)"yopf", {240, 157, 149, 170, 0}},
{(unsigned char*)"yscr", {240, 157, 147, 142, 0}},
{(unsigned char*)"yucy", {209, 142, 0}},
{(unsigned char*)"yuml", {195, 191, 0}},
{(unsigned char*)"zacute", {197, 186, 0}},
{(unsigned char*)"zcaron", {197, 190, 0}},
{(unsigned char*)"zcy", {208, 183, 0}},
{(unsigned char*)"zdot", {197, 188, 0}},
{(unsigned char*)"zeetrf", {226, 132, 168, 0}},
{(unsigned char*)"zeta", {206, 182, 0}},
{(unsigned char*)"zfr", {240, 157, 148, 183, 0}},
{(unsigned char*)"zhcy", {208, 182, 0}},
{(unsigned char*)"zigrarr", {226, 135, 157, 0}},
{(unsigned char*)"zopf", {240, 157, 149, 171, 0}},
{(unsigned char*)"zscr", {240, 157, 147, 143, 0}},
{(unsigned char*)"zwj", {226, 128, 141, 0}},
{(unsigned char*)"zwnj", {226, 128, 140, 0}},
};
