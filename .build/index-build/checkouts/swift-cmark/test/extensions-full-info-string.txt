---
title: --full-info-string test
author: Ashe Connor
version: 0.1
date: '2018-08-08'
license: '[CC-BY-SA 4.0](http://creativecommons.org/licenses/by-sa/4.0/)'
...

## `--full-info-string`

Without extended info:

```````````````````````````````` example
```ruby
module Foo
```
.
<pre><code class="language-ruby">module Foo
</code></pre>
````````````````````````````````

With extended info:

```````````````````````````````` example
```ruby some <extra> "data"
module Foo
```
.
<pre><code class="language-ruby" data-meta="some &lt;extra&gt; &quot;data&quot;">module Foo
</code></pre>
````````````````````````````````

With an embedded NUL:

```````````````````````````````` example
```ruby nul nul
module Foo
```
.
<pre><code class="language-ruby" data-meta="nul�nul">module Foo
</code></pre>
````````````````````````````````

With a lot:

```````````````````````````````` example
```ruby xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
module Foo
```
.
<pre><code class="language-ruby" data-meta="xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx">module Foo
</code></pre>
````````````````````````````````
