### Regression tests

Issue #113: EOL character weirdness on Windows
(Important: first line ends with CR + CR + LF)

```````````````````````````````` example
line1

line2
.
<p>line1</p>
<p>line2</p>
````````````````````````````````

Issue #114: cmark skipping first character in line
(Important: the blank lines around "Repeatedly" contain a tab.)

```````````````````````````````` example
By taking it apart

- alternative solutions
→
Repeatedly solving
→
- how techniques
.
<p>By taking it apart</p>
<ul>
<li>alternative solutions</li>
</ul>
<p>Repeatedly solving</p>
<ul>
<li>how techniques</li>
</ul>
````````````````````````````````

Issue jgm/CommonMark#430:  h2..h6 not recognized as block tags.

```````````````````````````````` example
<h1>lorem</h1>

<h2>lorem</h2>

<h3>lorem</h3>

<h4>lorem</h4>

<h5>lorem</h5>

<h6>lorem</h6>
.
<h1>lorem</h1>
<h2>lorem</h2>
<h3>lorem</h3>
<h4>lorem</h4>
<h5>lorem</h5>
<h6>lorem</h6>
````````````````````````````````

Issue jgm/commonmark.js#109 - tabs after setext header line


```````````````````````````````` example
hi
--→
.
<h2>hi</h2>
````````````````````````````````

Issue #177 - incorrect emphasis parsing

```````````````````````````````` example
a***b* c*
.
<p>a*<em><em>b</em> c</em></p>
````````````````````````````````

Issue #193 - unescaped left angle brackets in link destination

```````````````````````````````` example
[a]

[a]: <te<st>
.
<p>[a]</p>
<p>[a]: &lt;te<st></p>
````````````````````````````````

Issue #192 - escaped spaces in link destination


```````````````````````````````` example
[a](te\ st)
.
<p>[a](te\ st)</p>
````````````````````````````````

Issue github/github#76615:  multiple delimiter combinations gets sketchy


```````````````````````````````` example strikethrough
~~**_`this`_**~~  
~~***`this`***~~  
~~___`this`___~~

**_`this`_**  
***`this`***  
___`this`___

~~**_this_**~~  
~~***this***~~  
~~___this___~~

**_this_**  
***this***  
___this___
.
<p><del><strong><em><code>this</code></em></strong></del><br />
<del><em><strong><code>this</code></strong></em></del><br />
<del><em><strong><code>this</code></strong></em></del></p>
<p><strong><em><code>this</code></em></strong><br />
<em><strong><code>this</code></strong></em><br />
<em><strong><code>this</code></strong></em></p>
<p><del><strong><em>this</em></strong></del><br />
<del><em><strong>this</strong></em></del><br />
<del><em><strong>this</strong></em></del></p>
<p><strong><em>this</em></strong><br />
<em><strong>this</strong></em><br />
<em><strong>this</strong></em></p>
````````````````````````````````

Issue #527 - meta tags in inline contexts

```````````````````````````````` example
City:
<span itemprop="contentLocation" itemscope itemtype="https://schema.org/City">
  <meta itemprop="name" content="Springfield">
</span>
.
<p>City:
<span itemprop="contentLocation" itemscope itemtype="https://schema.org/City">
<meta itemprop="name" content="Springfield">
</span></p>
````````````````````````````````

cmark-gfm strikethrough rules

```````````````````````````````` example strikethrough
~Hi~ Hello, world!
.
<p><del>Hi</del> Hello, world!</p>
````````````````````````````````

```````````````````````````````` example strikethrough
This ~text~ ~~is~~ ~~~curious~~~.
.
<p>This <del>text</del> <del>is</del> ~~~curious~~~.</p>
````````````````````````````````

`~` should not be escaped in href — https://github.com/github/markup/issues/311

```````````````````````````````` example
[x](http://members.aon.at/~nkehrer/ibm_5110/emu5110.html)
.
<p><a href="http://members.aon.at/~nkehrer/ibm_5110/emu5110.html">x</a></p>
````````````````````````````````

Footnotes in tables

```````````````````````````````` example table footnotes
A footnote in a paragraph[^1]

| Column1   | Column2 |
| --------- | ------- |
| foot [^1] | note    |

[^1]: a footnote
.
<p>A footnote in a paragraph<sup class="footnote-ref"><a href="#fn-1" id="fnref-1" data-footnote-ref>1</a></sup></p>
<table>
<thead>
<tr>
<th>Column1</th>
<th>Column2</th>
</tr>
</thead>
<tbody>
<tr>
<td>foot <sup class="footnote-ref"><a href="#fn-1" id="fnref-1-2" data-footnote-ref>1</a></sup></td>
<td>note</td>
</tr>
</tbody>
</table>
<section class="footnotes" data-footnotes>
<ol>
<li id="fn-1">
<p>a footnote <a href="#fnref-1" class="footnote-backref" data-footnote-backref data-footnote-backref-idx="1" aria-label="Back to reference 1">↩</a> <a href="#fnref-1-2" class="footnote-backref" data-footnote-backref data-footnote-backref-idx="1-2" aria-label="Back to reference 1-2">↩<sup class="footnote-ref">2</sup></a></p>
</li>
</ol>
</section>
````````````````````````````````

Issue #527 - meta tags in inline contexts

```````````````````````````````` example
City:
<span itemprop="contentLocation" itemscope itemtype="https://schema.org/City">
  <meta itemprop="name" content="Springfield">
</span>
.
<p>City:
<span itemprop="contentLocation" itemscope itemtype="https://schema.org/City">
<meta itemprop="name" content="Springfield">
</span></p>
````````````````````````````````

Issue #530 - link parsing corner cases

```````````````````````````````` example
[a](\ b)

[a](<<b)

[a](<b
)
.
<p>[a](\ b)</p>
<p>[a](&lt;&lt;b)</p>
<p>[a](&lt;b
)</p>
````````````````````````````````

Issue commonmark#526 - unescaped ( in link title

```````````````````````````````` example
[link](url ((title))
.
<p>[link](url ((title))</p>
````````````````````````````````

Issue commonamrk#517 - script, pre, style close tag without
opener.

```````````````````````````````` example
</script>

</pre>

</style>
.
</script>
</pre>
</style>
````````````````````````````````

Issue #289.

```````````````````````````````` example
[a](<b) c>
.
<p>[a](&lt;b) c&gt;</p>
````````````````````````````````

Pull request #128 - Buffer overread in tables extension

```````````````````````````````` example table
|
-|
.
<p>|
-|</p>
````````````````````````````````

Footnotes may be nested inside other footnotes.

```````````````````````````````` example footnotes
This is some text. It has a citation.[^citation]

[^another-citation]: My second citation.

[^citation]: This is a long winded parapgraph that also has another citation.[^another-citation]
.
<p>This is some text. It has a citation.<sup class="footnote-ref"><a href="#fn-citation" id="fnref-citation" data-footnote-ref>1</a></sup></p>
<section class="footnotes" data-footnotes>
<ol>
<li id="fn-citation">
<p>This is a long winded parapgraph that also has another citation.<sup class="footnote-ref"><a href="#fn-another-citation" id="fnref-another-citation" data-footnote-ref>2</a></sup> <a href="#fnref-citation" class="footnote-backref" data-footnote-backref data-footnote-backref-idx="1" aria-label="Back to reference 1">↩</a></p>
</li>
<li id="fn-another-citation">
<p>My second citation. <a href="#fnref-another-citation" class="footnote-backref" data-footnote-backref data-footnote-backref-idx="2" aria-label="Back to reference 2">↩</a></p>
</li>
</ol>
</section>
````````````````````````````````

Footnotes are similar to, but should not be confused with, link references

```````````````````````````````` example footnotes
This is some text. It has two footnotes references, side-by-side without any spaces,[^footnote1][^footnote2] which are definitely not link references.

[^footnote1]: Hello.

[^footnote2]: Goodbye.
.
<p>This is some text. It has two footnotes references, side-by-side without any spaces,<sup class="footnote-ref"><a href="#fn-footnote1" id="fnref-footnote1" data-footnote-ref>1</a></sup><sup class="footnote-ref"><a href="#fn-footnote2" id="fnref-footnote2" data-footnote-ref>2</a></sup> which are definitely not link references.</p>
<section class="footnotes" data-footnotes>
<ol>
<li id="fn-footnote1">
<p>Hello. <a href="#fnref-footnote1" class="footnote-backref" data-footnote-backref data-footnote-backref-idx="1" aria-label="Back to reference 1">↩</a></p>
</li>
<li id="fn-footnote2">
<p>Goodbye. <a href="#fnref-footnote2" class="footnote-backref" data-footnote-backref data-footnote-backref-idx="2" aria-label="Back to reference 2">↩</a></p>
</li>
</ol>
</section>
````````````````````````````````

Footnotes may begin with or have a 'w' or a '_' in their reference label.

```````````````````````````````` example footnotes autolink
This is some text. Sometimes the autolinker splits up text into multiple nodes, hoping it will find a hyperlink, so this text has a footnote whose reference label begins with a `w`.[^widely-cited]

It has another footnote that contains many different characters (the autolinker was also breaking on `_`).[^sphinx-of-black-quartz_judge-my-vow-0123456789]

[^sphinx-of-black-quartz_judge-my-vow-0123456789]: so does this.

[^widely-cited]: this renders properly.
.
<p>This is some text. Sometimes the autolinker splits up text into multiple nodes, hoping it will find a hyperlink, so this text has a footnote whose reference label begins with a <code>w</code>.<sup class="footnote-ref"><a href="#fn-widely-cited" id="fnref-widely-cited" data-footnote-ref>1</a></sup></p>
<p>It has another footnote that contains many different characters (the autolinker was also breaking on <code>_</code>).<sup class="footnote-ref"><a href="#fn-sphinx-of-black-quartz_judge-my-vow-0123456789" id="fnref-sphinx-of-black-quartz_judge-my-vow-0123456789" data-footnote-ref>2</a></sup></p>
<section class="footnotes" data-footnotes>
<ol>
<li id="fn-widely-cited">
<p>this renders properly. <a href="#fnref-widely-cited" class="footnote-backref" data-footnote-backref data-footnote-backref-idx="1" aria-label="Back to reference 1">↩</a></p>
</li>
<li id="fn-sphinx-of-black-quartz_judge-my-vow-0123456789">
<p>so does this. <a href="#fnref-sphinx-of-black-quartz_judge-my-vow-0123456789" class="footnote-backref" data-footnote-backref data-footnote-backref-idx="2" aria-label="Back to reference 2">↩</a></p>
</li>
</ol>
</section>
````````````````````````````````

Footnotes interacting with strikethrough should not lead to a use-after-free

```````````````````````````````` example footnotes autolink strikethrough table
|Tot.....[^_a_]|
.
<p>|Tot.....[^_a_]|</p>
````````````````````````````````

Footnotes interacting with strikethrough should not lead to a use-after-free pt2

```````````````````````````````` example footnotes autolink strikethrough table
[^~~is~~1]
.
<p>[^~~is~~1]</p>
````````````````````````````````

Adjacent unused footnotes definitions should not lead to a use after free

```````````````````````````````` example footnotes autolink strikethrough table
Hello world


[^a]:[^b]:
.
<p>Hello world</p>
````````````````````````````````

Issue #424 - emphasis before links

```````````````````````````````` example
*text* [link](#section)
.
<p><em>text</em> <a href="#section">link</a></p>
````````````````````````````````
