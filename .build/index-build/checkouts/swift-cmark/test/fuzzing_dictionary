asterisk="*"
attr_generic=" a=\"1\""
attr_href=" href=\"1\""
attr_xml_lang=" xml:lang=\"1\""
attr_xmlns=" xmlns=\"1\""
backslash="\\"
backtick="`"
colon=":"
dashes="---"
double_quote="\""
entity_builtin="&lt;"
entity_decimal="&#1;"
entity_external="&a;"
entity_hex="&#x1;"
equals="==="
exclamation="!"
greater_than=">"
hash="#"
hyphen="-"
indent="  "
left_bracket="["
left_paren="("
less_than="<"
plus="+"
right_bracket="]"
right_paren=")"
single_quote="'"
string_any="ANY"
string_brackets="[]"
string_cdata="CDATA"
string_dashes="--"
string_empty_dblquotes="\"\""
string_empty_quotes="''"
string_idrefs="IDREFS"
string_parentheses="()"
string_pcdata="#PCDATA"
tag_cdata="<![CDATA["
tag_close="</a>"
tag_doctype="<!DOCTYPE"
tag_element="<!ELEMENT"
tag_entity="<!ENTITY"
tag_notation="<!NOTATION"
tag_open="<a>"
tag_open_close="<a />"
tag_open_exclamation="<!"
tag_open_q="<?"
tag_sq2_close="]]>"
tag_xml_q="<?xml?>"
underscore="_"

# GFM specific

strikethrough="~~~strike~~~"
user_mention="@octocat"
email_mention="<EMAIL>"
http="http://"
https="https://"
ftp="ftp://"
title_tag="title"
textarea_tag="textarea"
style_tag="style"
xmp_tag="xmp"
iframe_tag="iframe"
noembed_tag="noembed"
noframes_tag="noframes"
script_tag="script"
plaintext_tag="plaintext"
