---
title: Extensions test
author: <PERSON><PERSON>
version: 0.1
date: '2016-08-31'
license: '[CC-BY-SA 4.0](http://creativecommons.org/licenses/by-sa/4.0/)'
...

## Tables

Here's a well-formed table, doing everything it should.

```````````````````````````````` example
| abc | def |
| --- | --- |
| ghi | jkl |
| mno | pqr |
.
<table>
<thead>
<tr>
<th>abc</th>
<th>def</th>
</tr>
</thead>
<tbody>
<tr>
<td>ghi</td>
<td>jkl</td>
</tr>
<tr>
<td>mno</td>
<td>pqr</td>
</tr>
</tbody>
</table>
````````````````````````````````

We're going to mix up the table now; we'll demonstrate that inline formatting
works fine, but block elements don't.  You can also have empty cells, and the
textual alignment of the columns is shown to be irrelevant.

```````````````````````````````` example
Hello!

| _abc_ | セン |
| ----- | ---- |
| 1. Block elements inside cells don't work. | |
| But _**inline elements do**_. | x |

Hi!
.
<p>Hello!</p>
<table>
<thead>
<tr>
<th><em>abc</em></th>
<th>セン</th>
</tr>
</thead>
<tbody>
<tr>
<td>1. Block elements inside cells don't work.</td>
<td></td>
</tr>
<tr>
<td>But <em><strong>inline elements do</strong></em>.</td>
<td>x</td>
</tr>
</tbody>
</table>
<p>Hi!</p>
````````````````````````````````

Here we demonstrate some edge cases about what is and isn't a table.

```````````````````````````````` example
| Not enough table | to be considered table |

| Not enough table | to be considered table |
| Not enough table | to be considered table |

| Just enough table | to be considered table |
| ----------------- | ---------------------- |

| ---- | --- |

|x|
|-|

| xyz |
| --- |
.
<p>| Not enough table | to be considered table |</p>
<p>| Not enough table | to be considered table |
| Not enough table | to be considered table |</p>
<table>
<thead>
<tr>
<th>Just enough table</th>
<th>to be considered table</th>
</tr>
</thead>
</table>
<p>| ---- | --- |</p>
<table>
<thead>
<tr>
<th>x</th>
</tr>
</thead>
</table>
<table>
<thead>
<tr>
<th>xyz</th>
</tr>
</thead>
</table>
````````````````````````````````

A "simpler" table, GFM style:

```````````````````````````````` example
abc | def
--- | ---
xyz | ghi
.
<table>
<thead>
<tr>
<th>abc</th>
<th>def</th>
</tr>
</thead>
<tbody>
<tr>
<td>xyz</td>
<td>ghi</td>
</tr>
</tbody>
</table>
````````````````````````````````

We are making the parser slighly more lax here. Here is a table with spaces at
the end:

```````````````````````````````` example
Hello!

| _abc_ | セン |
| ----- | ---- |
| this row has a space at the end | | 
| But _**inline elements do**_. | x |

Hi!
.
<p>Hello!</p>
<table>
<thead>
<tr>
<th><em>abc</em></th>
<th>セン</th>
</tr>
</thead>
<tbody>
<tr>
<td>this row has a space at the end</td>
<td></td>
</tr>
<tr>
<td>But <em><strong>inline elements do</strong></em>.</td>
<td>x</td>
</tr>
</tbody>
</table>
<p>Hi!</p>
````````````````````````````````

Table alignment:

```````````````````````````````` example
aaa | bbb | ccc | ddd | eee
:-- | --- | :-: | --- | --:
fff | ggg | hhh | iii | jjj
.
<table>
<thead>
<tr>
<th align="left">aaa</th>
<th>bbb</th>
<th align="center">ccc</th>
<th>ddd</th>
<th align="right">eee</th>
</tr>
</thead>
<tbody>
<tr>
<td align="left">fff</td>
<td>ggg</td>
<td align="center">hhh</td>
<td>iii</td>
<td align="right">jjj</td>
</tr>
</tbody>
</table>
````````````````````````````````

### Table cell count mismatches

The header and delimiter row must match.

```````````````````````````````` example
| a | b | c |
| --- | --- |
| this | isn't | okay |
.
<p>| a | b | c |
| --- | --- |
| this | isn't | okay |</p>
````````````````````````````````

But any of the body rows can be shorter. Rows longer
than the header are truncated.

```````````````````````````````` example
| a | b | c |
| --- | --- | ---
| x
| a | b
| 1 | 2 | 3 | 4 | 5 |
.
<table>
<thead>
<tr>
<th>a</th>
<th>b</th>
<th>c</th>
</tr>
</thead>
<tbody>
<tr>
<td>x</td>
<td></td>
<td></td>
</tr>
<tr>
<td>a</td>
<td>b</td>
<td></td>
</tr>
<tr>
<td>1</td>
<td>2</td>
<td>3</td>
</tr>
</tbody>
</table>
````````````````````````````````

### Embedded pipes

Tables with embedded pipes could be tricky.

```````````````````````````````` example
| a | b |
| --- | --- |
| Escaped pipes are \|okay\|. | Like \| this. |
| Within `\|code\| is okay` too. |
| _**`c\|`**_ \| complex
| don't **\_reparse\_**
.
<table>
<thead>
<tr>
<th>a</th>
<th>b</th>
</tr>
</thead>
<tbody>
<tr>
<td>Escaped pipes are |okay|.</td>
<td>Like | this.</td>
</tr>
<tr>
<td>Within <code>|code| is okay</code> too.</td>
<td></td>
</tr>
<tr>
<td><em><strong><code>c|</code></strong></em> | complex</td>
<td></td>
</tr>
<tr>
<td>don't <strong>_reparse_</strong></td>
<td></td>
</tr>
</tbody>
</table>
````````````````````````````````

### Oddly-formatted markers

This shouldn't assert.

```````````````````````````````` example
| a |
--- |
.
<table>
<thead>
<tr>
<th>a</th>
</tr>
</thead>
</table>
````````````````````````````````

### Escaping

```````````````````````````````` example
| a | b |
| --- | --- |
| \\ | `\\` |
| \\\\ | `\\\\` |
| \_ | `\_` |
| \| | `\|` |
| \a | `\a` |

\\ `\\`

\\\\ `\\\\`

\_ `\_`

\| `\|`

\a `\a`
.
<table>
<thead>
<tr>
<th>a</th>
<th>b</th>
</tr>
</thead>
<tbody>
<tr>
<td>\</td>
<td><code>\\</code></td>
</tr>
<tr>
<td>\\</td>
<td><code>\\\\</code></td>
</tr>
<tr>
<td>_</td>
<td><code>\_</code></td>
</tr>
<tr>
<td>|</td>
<td><code>|</code></td>
</tr>
<tr>
<td>\a</td>
<td><code>\a</code></td>
</tr>
</tbody>
</table>
<p>\ <code>\\</code></p>
<p>\\ <code>\\\\</code></p>
<p>_ <code>\_</code></p>
<p>| <code>\|</code></p>
<p>\a <code>\a</code></p>
````````````````````````````````

### Embedded HTML

```````````````````````````````` example
| a |
| --- |
| <strong>hello</strong> |
| ok <br> sure |
.
<table>
<thead>
<tr>
<th>a</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>hello</strong></td>
</tr>
<tr>
<td>ok <br> sure</td>
</tr>
</tbody>
</table>
````````````````````````````````

### Reference-style links

```````````````````````````````` example
Here's a link to [Freedom Planet 2][].

| Here's a link to [Freedom Planet 2][] in a table header. |
| --- |
| Here's a link to [Freedom Planet 2][] in a table row. |

[Freedom Planet 2]: http://www.freedomplanet2.com/
.
<p>Here's a link to <a href="http://www.freedomplanet2.com/">Freedom Planet 2</a>.</p>
<table>
<thead>
<tr>
<th>Here's a link to <a href="http://www.freedomplanet2.com/">Freedom Planet 2</a> in a table header.</th>
</tr>
</thead>
<tbody>
<tr>
<td>Here's a link to <a href="http://www.freedomplanet2.com/">Freedom Planet 2</a> in a table row.</td>
</tr>
</tbody>
</table>
````````````````````````````````

### Sequential cells

```````````````````````````````` example
| a | b | c |
| --- | --- | --- |
| d || e |
.
<table>
<thead>
<tr>
<th>a</th>
<th>b</th>
<th>c</th>
</tr>
</thead>
<tbody>
<tr>
<td>d</td>
<td></td>
<td>e</td>
</tr>
</tbody>
</table>
````````````````````````````````

### Interaction with emphasis

```````````````````````````````` example
| a | b |
| --- | --- |
|***(a)***|
.
<table>
<thead>
<tr>
<th>a</th>
<th>b</th>
</tr>
</thead>
<tbody>
<tr>
<td><em><strong>(a)</strong></em></td>
<td></td>
</tr>
</tbody>
</table>
````````````````````````````````

### a table can be recognised when separated from a paragraph of text without an empty line

```````````````````````````````` example
123
456
| a | b |
| ---| --- |
d | e
.
<p>123
456</p>
<table>
<thead>
<tr>
<th>a</th>
<th>b</th>
</tr>
</thead>
<tbody>
<tr>
<td>d</td>
<td>e</td>
</tr>
</tbody>
</table>
````````````````````````````````

## Strikethroughs

A well-formed strikethrough.

```````````````````````````````` example
A proper ~strikethrough~.
.
<p>A proper <del>strikethrough</del>.</p>
````````````````````````````````

Some strikethrough edge cases.

```````````````````````````````` example
These are ~not strikethroughs.

No, they are not~

This ~is ~ legit~ isn't ~ legit.

This is not ~~~~~one~~~~~ huge strikethrough.

~one~ ~~two~~ ~~~three~~~

No ~mismatch~~
.
<p>These are ~not strikethroughs.</p>
<p>No, they are not~</p>
<p>This <del>is ~ legit</del> isn't ~ legit.</p>
<p>This is not ~~~~~one~~~~~ huge strikethrough.</p>
<p><del>one</del> <del>two</del> ~~~three~~~</p>
<p>No ~mismatch~~</p>
````````````````````````````````

Using 200 tilde since it overflows the internal buffer
size (100) for parsing delimiters in inlines.c
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~striked~

## Autolinks

```````````````````````````````` example
: http://google.com https://google.com

<http://google.com/å> http://google.com/å

<EMAIL>

<EMAIL>

<EMAIL>.

<EMAIL>/

<EMAIL>/<EMAIL>

mailto:<EMAIL>

This is a mailto:<EMAIL>

mailto:<EMAIL>.

mmmmailto:<EMAIL>

mailto:<EMAIL>/

mailto:<EMAIL>/message

mailto:<EMAIL>/mailto:<EMAIL>

xmpp:<EMAIL>

xmpp:<EMAIL>.

xmpp:<EMAIL>/message

xmpp:<EMAIL>/message.

Email me at:<EMAIL>

www.github.com www.github.com/á

www.google.com/a_b

Underscores not allowed in host name www.xxx.yyy._zzz

Underscores not allowed in host name www.xxx._yyy.zzz

Underscores allowed in domain name www._xxx.yyy.zzz

**Autolink and http://inlines**

![http://inline.com/image](http://inline.com/image)

a.w@b.c

Full stop outside parens shouldn't be included http://google.com/ok.

(Full stop inside parens shouldn't be included http://google.com/ok.)

"http://google.com"

'http://google.com'

http://🍄.ga/ http://x🍄.ga/
.
<p>: <a href="http://google.com">http://google.com</a> <a href="https://google.com">https://google.com</a></p>
<p><a href="http://google.com/%C3%A5">http://google.com/å</a> <a href="http://google.com/%C3%A5">http://google.com/å</a></p>
<p><a href="mailto:<EMAIL>"><EMAIL></a></p>
<p><a href="mailto:<EMAIL>"><EMAIL></a></p>
<p><a href="mailto:<EMAIL>"><EMAIL></a>.</p>
<p><a href="mailto:<EMAIL>"><EMAIL></a>/</p>
<p><a href="mailto:<EMAIL>"><EMAIL></a>/<a href="mailto:<EMAIL>"><EMAIL></a></p>
<p><a href="mailto:<EMAIL>">mailto:<EMAIL></a></p>
<p>This is a <a href="mailto:<EMAIL>">mailto:<EMAIL></a></p>
<p><a href="mailto:<EMAIL>">mailto:<EMAIL></a>.</p>
<p>mmmmailto:<a href="mailto:<EMAIL>"><EMAIL></a></p>
<p><a href="mailto:<EMAIL>">mailto:<EMAIL></a>/</p>
<p><a href="mailto:<EMAIL>">mailto:<EMAIL></a>/message</p>
<p><a href="mailto:<EMAIL>">mailto:<EMAIL></a>/<a href="mailto:<EMAIL>">mailto:<EMAIL></a></p>
<p><a href="xmpp:<EMAIL>">xmpp:<EMAIL></a></p>
<p><a href="xmpp:<EMAIL>">xmpp:<EMAIL></a>.</p>
<p><a href="xmpp:<EMAIL>/message">xmpp:<EMAIL>/message</a></p>
<p><a href="xmpp:<EMAIL>/message">xmpp:<EMAIL>/message</a>.</p>
<p>Email me at:<a href="mailto:<EMAIL>"><EMAIL></a></p>
<p><a href="http://www.github.com">www.github.com</a> <a href="http://www.github.com/%C3%A1">www.github.com/á</a></p>
<p><a href="http://www.google.com/a_b">www.google.com/a_b</a></p>
<p>Underscores not allowed in host name www.xxx.yyy._zzz</p>
<p>Underscores not allowed in host name www.xxx._yyy.zzz</p>
<p>Underscores allowed in domain name <a href="http://www._xxx.yyy.zzz">www._xxx.yyy.zzz</a></p>
<p><strong>Autolink and <a href="http://inlines">http://inlines</a></strong></p>
<p><img src="http://inline.com/image" alt="http://inline.com/image" /></p>
<p><a href="mailto:a.w@b.c">a.w@b.c</a></p>
<p>Full stop outside parens shouldn't be included <a href="http://google.com/ok">http://google.com/ok</a>.</p>
<p>(Full stop inside parens shouldn't be included <a href="http://google.com/ok">http://google.com/ok</a>.)</p>
<p>&quot;<a href="http://google.com">http://google.com</a>&quot;</p>
<p>'<a href="http://google.com">http://google.com</a>'</p>
<p><a href="http://%F0%9F%8D%84.ga/">http://🍄.ga/</a> <a href="http://x%F0%9F%8D%84.ga/">http://x🍄.ga/</a></p>
````````````````````````````````

```````````````````````````````` example
This shouldn't crash everything: (_A_@_.A
.
<IGNORE>
````````````````````````````````

```````````````````````````````` example
These should not link:

* @a.b.c@. x
* n@.  b
.
<p>These should not link:</p>
<ul>
<li>@a.b.c@. x</li>
<li>n@.  b</li>
</ul>
````````````````````````````````

## HTML tag filter


```````````````````````````````` example
This is <xmp> not okay, but **this** <strong>is</strong>.

<p>This is <xmp> not okay, but **this** <strong>is</strong>.</p>

Nope, I won't have <textarea>.

<p>No <textarea> here either.</p>

<p>This <random /> <thing> is okay</thing> though.</p>

Yep, <totally>okay</totally>.

<!-- HTML comments are okay, though. -->
<!- But we're strict. ->
<! No nonsense. >
<!-- Leave multiline comments the heck alone, though, okay?
Even with {"x":"y"} or 1 > 2 or whatever. Even **markdown**.
-->
<!--- Support everything CommonMark's parser does. -->
<!---->
<!--thistoo-->
.
<p>This is &lt;xmp> not okay, but <strong>this</strong> <strong>is</strong>.</p>
<p>This is &lt;xmp> not okay, but **this** <strong>is</strong>.</p>
<p>Nope, I won't have &lt;textarea>.</p>
<p>No &lt;textarea> here either.</p>
<p>This <random /> <thing> is okay</thing> though.</p>
<p>Yep, <totally>okay</totally>.</p>
<!-- HTML comments are okay, though. -->
<p>&lt;!- But we're strict. -&gt;
&lt;! No nonsense. &gt;</p>
<!-- Leave multiline comments the heck alone, though, okay?
Even with {"x":"y"} or 1 > 2 or whatever. Even **markdown**.
-->
<!--- Support everything CommonMark's parser does. -->
<!---->
<!--thistoo-->
````````````````````````````````

## Footnotes

```````````````````````````````` example
This is some text![^1]. Other text.[^footnote].

Here's a thing[^other-note].

And another thing[^codeblock-note].

This doesn't have a referent[^nope].


[^other-note]:       no code block here (spaces are stripped away)

[^codeblock-note]:
        this is now a code block (8 spaces indentation)

[^1]: Some *bolded* footnote definition.

Hi!

[^footnote]:
    > Blockquotes can be in a footnote.

        as well as code blocks

    or, naturally, simple paragraphs.

[^unused]: This is unused.
.
<p>This is some text!<sup class="footnote-ref"><a href="#fn-1" id="fnref-1" data-footnote-ref>1</a></sup>. Other text.<sup class="footnote-ref"><a href="#fn-footnote" id="fnref-footnote" data-footnote-ref>2</a></sup>.</p>
<p>Here's a thing<sup class="footnote-ref"><a href="#fn-other-note" id="fnref-other-note" data-footnote-ref>3</a></sup>.</p>
<p>And another thing<sup class="footnote-ref"><a href="#fn-codeblock-note" id="fnref-codeblock-note" data-footnote-ref>4</a></sup>.</p>
<p>This doesn't have a referent[^nope].</p>
<p>Hi!</p>
<section class="footnotes" data-footnotes>
<ol>
<li id="fn-1">
<p>Some <em>bolded</em> footnote definition. <a href="#fnref-1" class="footnote-backref" data-footnote-backref data-footnote-backref-idx="1" aria-label="Back to reference 1">↩</a></p>
</li>
<li id="fn-footnote">
<blockquote>
<p>Blockquotes can be in a footnote.</p>
</blockquote>
<pre><code>as well as code blocks
</code></pre>
<p>or, naturally, simple paragraphs. <a href="#fnref-footnote" class="footnote-backref" data-footnote-backref data-footnote-backref-idx="2" aria-label="Back to reference 2">↩</a></p>
</li>
<li id="fn-other-note">
<p>no code block here (spaces are stripped away) <a href="#fnref-other-note" class="footnote-backref" data-footnote-backref data-footnote-backref-idx="3" aria-label="Back to reference 3">↩</a></p>
</li>
<li id="fn-codeblock-note">
<pre><code>this is now a code block (8 spaces indentation)
</code></pre>
<a href="#fnref-codeblock-note" class="footnote-backref" data-footnote-backref data-footnote-backref-idx="4" aria-label="Back to reference 4">↩</a>
</li>
</ol>
</section>
````````````````````````````````

## When a footnote is used multiple times, we insert multiple backrefs.

```````````````````````````````` example
This is some text. It has a footnote[^a-footnote].

This footnote is referenced[^a-footnote] multiple times, in lots of different places.[^a-footnote]

[^a-footnote]: This footnote definition should have three backrefs.
.
<p>This is some text. It has a footnote<sup class="footnote-ref"><a href="#fn-a-footnote" id="fnref-a-footnote" data-footnote-ref>1</a></sup>.</p>
<p>This footnote is referenced<sup class="footnote-ref"><a href="#fn-a-footnote" id="fnref-a-footnote-2" data-footnote-ref>1</a></sup> multiple times, in lots of different places.<sup class="footnote-ref"><a href="#fn-a-footnote" id="fnref-a-footnote-3" data-footnote-ref>1</a></sup></p>
<section class="footnotes" data-footnotes>
<ol>
<li id="fn-a-footnote">
<p>This footnote definition should have three backrefs. <a href="#fnref-a-footnote" class="footnote-backref" data-footnote-backref data-footnote-backref-idx="1" aria-label="Back to reference 1">↩</a> <a href="#fnref-a-footnote-2" class="footnote-backref" data-footnote-backref data-footnote-backref-idx="1-2" aria-label="Back to reference 1-2">↩<sup class="footnote-ref">2</sup></a> <a href="#fnref-a-footnote-3" class="footnote-backref" data-footnote-backref data-footnote-backref-idx="1-3" aria-label="Back to reference 1-3">↩<sup class="footnote-ref">3</sup></a></p>
</li>
</ol>
</section>
````````````````````````````````

## Footnote reference labels are href escaped

```````````````````````````````` example
Hello[^"><script>alert(1)</script>]

[^"><script>alert(1)</script>]: pwned
.
<p>Hello<sup class="footnote-ref"><a href="#fn-%22%3E%3Cscript%3Ealert(1)%3C/script%3E" id="fnref-%22%3E%3Cscript%3Ealert(1)%3C/script%3E" data-footnote-ref>1</a></sup></p>
<section class="footnotes" data-footnotes>
<ol>
<li id="fn-%22%3E%3Cscript%3Ealert(1)%3C/script%3E">
<p>pwned <a href="#fnref-%22%3E%3Cscript%3Ealert(1)%3C/script%3E" class="footnote-backref" data-footnote-backref data-footnote-backref-idx="1" aria-label="Back to reference 1">↩</a></p>
</li>
</ol>
</section>
````````````````````````````````

## Interop

Autolink and strikethrough.

```````````````````````````````` example
~~www.google.com~~

~~http://google.com~~
.
<p><del><a href="http://www.google.com">www.google.com</a></del></p>
<p><del><a href="http://google.com">http://google.com</a></del></p>
````````````````````````````````

Autolink and tables.

```````````````````````````````` example
| a | b |
| --- | --- |
| https://github.com www.github.com | http://pokemon.com |
.
<table>
<thead>
<tr>
<th>a</th>
<th>b</th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="https://github.com">https://github.com</a> <a href="http://www.github.com">www.github.com</a></td>
<td><a href="http://pokemon.com">http://pokemon.com</a></td>
</tr>
</tbody>
</table>
````````````````````````````````

## Task lists

```````````````````````````````` example
- [ ] foo
- [x] bar
.
<ul>
<li><input type="checkbox" disabled="" /> foo</li>
<li><input type="checkbox" checked="" disabled="" /> bar</li>
</ul>
````````````````````````````````

Show that a task list and a regular list get processed the same in
the way that sublists are created. If something works in a list
item, then it should work the same way with a task.  The only
difference should be the tasklist marker. So, if we use something
other than a space or x, it won't be recognized as a task item, and
so will be treated as a regular item.

```````````````````````````````` example
- [x] foo
  - [ ] bar
  - [x] baz
- [ ] bim

Show a regular (non task) list to show that it has the same structure
- [@] foo
  - [@] bar
  - [@] baz
- [@] bim
.
<ul>
<li><input type="checkbox" checked="" disabled="" /> foo
<ul>
<li><input type="checkbox" disabled="" /> bar</li>
<li><input type="checkbox" checked="" disabled="" /> baz</li>
</ul>
</li>
<li><input type="checkbox" disabled="" /> bim</li>
</ul>
<p>Show a regular (non task) list to show that it has the same structure</p>
<ul>
<li>[@] foo
<ul>
<li>[@] bar</li>
<li>[@] baz</li>
</ul>
</li>
<li>[@] bim</li>
</ul>
````````````````````````````````
Use a larger indent -- a task list and a regular list should produce
the same structure.

```````````````````````````````` example
- [x] foo
    - [ ] bar
    - [x] baz
- [ ] bim

Show a regular (non task) list to show that it has the same structure
- [@] foo
    - [@] bar
    - [@] baz
- [@] bim
.
<ul>
<li><input type="checkbox" checked="" disabled="" /> foo
<ul>
<li><input type="checkbox" disabled="" /> bar</li>
<li><input type="checkbox" checked="" disabled="" /> baz</li>
</ul>
</li>
<li><input type="checkbox" disabled="" /> bim</li>
</ul>
<p>Show a regular (non task) list to show that it has the same structure</p>
<ul>
<li>[@] foo
<ul>
<li>[@] bar</li>
<li>[@] baz</li>
</ul>
</li>
<li>[@] bim</li>
</ul>
````````````````````````````````
