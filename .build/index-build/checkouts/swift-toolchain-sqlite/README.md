swift-toolchain-sqlite
======================

The [SQLite Amalgamation](https://sqlite.org/amalgamation.html) from sqlite.org as a Swift package.

This is intended as a common infrastructure package for components of the Swift toolchain which depend on SQLite.
It is not intended as a general-purpose SQLite package, nor does it aim to provide Swift wrapper APIs.

This package has been tested on macOS, iOS, tvOS, watchOS, visionOS, Mac Catalyst, Linux, Windows, and WebAssembly.


Contributing to /swift-toolchain-sqlite
-------------

Contributions to /swift-toolchain-sqlite are welcomed and encouraged! Please see the
[Contributing to <PERSON> guide](https://swift.org/contributing/).

Before submitting the pull request, please make sure you have [tested your
 changes](https://github.com/swiftlang/swift/blob/main/docs/ContinuousIntegration.md)
 and that they follow the Swift project [guidelines for contributing
 code](https://swift.org/contributing/#contributing-code). Bug reports should be 
 filed in [the issue tracker](https://github.com/swiftlang/swift-toolchain-sqlite/issues) of 
 `swift-toolchain-sqlite` repository on GitHub.

To be a truly great community, [Swift.org](https://swift.org/) needs to welcome
developers from all walks of life, with different backgrounds, and with a wide
range of experience. A diverse and friendly community will have more great
ideas, more unique perspectives, and produce more great code. We will work
diligently to make the Swift community welcoming to everyone.

To give clarity of what is expected of our members, Swift has adopted the
code of conduct defined by the Contributor Covenant. This document is used
across many open source communities, and we think it articulates our values
well. For more, see the [Code of Conduct](https://swift.org/code-of-conduct/).


License
-------
See https://swift.org/LICENSE.txt for license information.
