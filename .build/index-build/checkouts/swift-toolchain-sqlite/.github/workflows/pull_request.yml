name: Pull request

on:
  pull_request:
    types: [opened, reopened, synchronize]

jobs:
  tests:
    name: Test
    uses: swiftlang/github-workflows/.github/workflows/swift_package_test.yml@main
    with:
      linux_build_command: "swift build"
      linux_exclude_swift_versions: '[{"swift_version": "5.8"}]'
      windows_build_command: "swift build"
      macos_build_command: "xcrun swift build"
      enable_macos_checks: true

  soundness:
    name: Soundness
    uses: swiftlang/github-workflows/.github/workflows/soundness.yml@main
    with:
      license_header_check_project_name: "Swift.org"
      license_header_check_enabled: false
      unacceptable_language_check_enabled: false
