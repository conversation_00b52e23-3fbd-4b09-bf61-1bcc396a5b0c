# This file is a list of the people responsible for ensuring that patches for a
# particular part of Swift are reviewed, either by themself or by someone else.
# They are also the gatekeepers for their part of Swift, with the final word on
# what goes in or not.

# The list is sorted by surname and formatted to allow easy grepping and
# beautification by scripts.  The fields are: name (N), email (E), web-address
# (W), PGP key ID and fingerprint (P), description (D), and snail-mail address
# (S).

# N: <PERSON>
# E: <EMAIL>
# D: Everything in swift-toolchain-sqlite not covered by someone else

###

# The following lines are used by GitHub to automatically recommend reviewers.

* @jakepetroules
