#!/bin/bash
#
# This source file is part of the Swift.org open source project
#
# Copyright (c) 2021 Apple Inc. and the Swift project authors
# Licensed under Apache License v2.0 with Runtime Library Exception
#
# See https://swift.org/LICENSE.txt for license information
# See https://swift.org/CONTRIBUTORS.txt for Swift project authors
#

set -eu

# A `realpath` alternative using the default C implementation.
filepath() {
  [[ $1 = /* ]] && echo "$1" || echo "$PWD/${1#./}"
}

# First get the absolute path to this file so we can get the absolute file path to the SwiftMarkdown root source dir.
SWIFT_MARKDOWN_ROOT="$(dirname $(dirname $(filepath $0)))"

# Build SwiftMarkdown.
swift test --parallel --package-path "$SWIFT_MARKDOWN_ROOT"

# Run source code checks for the codebase.
LC_ALL=C "$SWIFT_MARKDOWN_ROOT"/bin/check-source

# Test utility scripts validity.
printf "=> Validating scripts in bin subdirectory… "

printf "\033[0;32mokay.\033[0m\n"

