#!/bin/bash
#
# This source file is part of the Swift.org open source project
#
# Copyright (c) 2021-2024 Apple Inc. and the Swift project authors
# Licensed under Apache License v2.0 with Runtime Library Exception
#
# See https://swift.org/LICENSE.txt for license information
# See https://swift.org/CONTRIBUTORS.txt for Swift project authors
#

# This script performs code checks such as verifying that source files
# use the correct license header. Its contents are largely copied from
# https://github.com/apple/swift-nio/blob/main/scripts/soundness.sh

set -eu
here="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

function replace_acceptable_years() {
    # this needs to replace all acceptable forms with 'YEARS'
    sed -e 's/20[12][78901234]-20[12][8901234]/YEARS/' -e 's/20[12][8901234]/YEARS/'
}

printf "=> Checking for unacceptable language… "
# This greps for unacceptable terminology. The square bracket[s] are so that
# "git grep" doesn't find the lines that greps :).
unacceptable_terms=(
    -e blacklis[t]
    -e whitelis[t]
    -e slav[e]
)

if git grep --color=never -i "${unacceptable_terms[@]}" > /dev/null; then
    printf "\033[0;31mUnacceptable language found.\033[0m\n"
    git grep -i "${unacceptable_terms[@]}"
    exit 1
fi
printf "\033[0;32mokay.\033[0m\n"

printf "=> Checking license headers… "
tmp=$(mktemp /tmp/.swift-markdown-check-source_XXXXXX)

for language in swift-or-c snippet bash md-or-tutorial html docker; do
  declare -a matching_files
  declare -a exceptions
  declare -a reader
  expections=( )
  matching_files=( -name '*' )
  reader=head
  case "$language" in
      swift-or-c)
        exceptions=( -name 'Package*.swift' -o -path './Snippets/*')
        matching_files=( -name '*.swift' -o -name '*.c' -o -name '*.h' )
        cat > "$tmp" <<"EOF"
/*
 This source file is part of the Swift.org open source project

 Copyright (c) YEARS Apple Inc. and the Swift project authors
 Licensed under Apache License v2.0 with Runtime Library Exception

 See https://swift.org/LICENSE.txt for license information
 See https://swift.org/CONTRIBUTORS.txt for Swift project authors
*/
EOF
        ;;
      snippet)
        matching_files=( -name '*.swift' -a -path './Snippets/*')
        exceptions=( -name 'Package*.swift')
        reader=tail
        cat > "$tmp" <<"EOF"
/*
 This source file is part of the Swift.org open source project

 Copyright (c) YEARS Apple Inc. and the Swift project authors
 Licensed under Apache License v2.0 with Runtime Library Exception

 See https://swift.org/LICENSE.txt for license information
 See https://swift.org/CONTRIBUTORS.txt for Swift project authors
*/
EOF
        ;;
      bash)
        matching_files=( -name '*.sh' )
        cat > "$tmp" <<"EOF"
#!/bin/bash
#
# This source file is part of the Swift.org open source project
#
# Copyright (c) YEARS Apple Inc. and the Swift project authors
# Licensed under Apache License v2.0 with Runtime Library Exception
#
# See https://swift.org/LICENSE.txt for license information
# See https://swift.org/CONTRIBUTORS.txt for Swift project authors
#
EOF
      ;;
      md-or-tutorial)
        exceptions=( -path "./.github/*.md")
        matching_files=( -name '*.md' -o -name '*.tutorial' )
        reader=tail
        cat > "$tmp" <<"EOF"
<!-- Copyright (c) YEARS Apple Inc and the Swift Project authors. All Rights Reserved. -->
EOF
      ;;
      html)
        matching_files=( -name '*.html' )
        cat > "$tmp" <<"EOF"
<!--
  This source file is part of the Swift.org open source project

  Copyright (c) YEARS Apple Inc. and the Swift project authors
  Licensed under Apache License v2.0 with Runtime Library Exception

  See https://swift.org/LICENSE.txt for license information
  See https://swift.org/CONTRIBUTORS.txt for Swift project authors
-->
EOF
      ;;
      docker)
        matching_files=( -name 'Dockerfile' )
        cat > "$tmp" <<"EOF"
# This source file is part of the Swift.org open source project
#
# Copyright (c) YEARS Apple Inc. and the Swift project authors
# Licensed under Apache License v2.0 with Runtime Library Exception
#
# See https://swift.org/LICENSE.txt for license information
# See https://swift.org/CONTRIBUTORS.txt for Swift project authors
EOF
      ;;
    *)
      echo >&2 "ERROR: unknown language '$language'"
      ;;
  esac

  # Determine which SHA command to use; not all platforms support shasum, but they
  # likely either support shasum or sha256sum.
  SHA_CMD=""
  if [ -x "$(command -v shasum)" ]; then
    SHA_CMD="shasum"
  elif [ -x "$(command -v sha256sum)" ]; then
    SHA_CMD="sha256sum"
  else
    echo >&2 "No sha command found; install shasum or sha256sum or submit a PR that supports another platform"
  fi

  expected_lines=$(cat "$tmp" | wc -l)
  expected_sha=$(cat "$tmp" | "$SHA_CMD")

  (
    cd "$here/.."
    find . \
      \( \! -path './.build/*' -a \
      \! -name '.' -a \
      \( "${matching_files[@]}" \) -a \
      \( \! \( "${exceptions[@]}" \) \) \) | while read line; do
      if [[ "$(cat "$line" | replace_acceptable_years | $reader -n $expected_lines | $SHA_CMD)" != "$expected_sha" ]]; then
        printf "\033[0;31mmissing headers in file '$line'!\033[0m\n"
        diff -u <(cat "$line" | replace_acceptable_years | $reader -n $expected_lines) "$tmp"
        exit 1
      fi
    done
  )
done

printf "\033[0;32mokay.\033[0m\n"
rm "$tmp"

# vim: filetype=bash shiftwidth=2 softtabstop=2
