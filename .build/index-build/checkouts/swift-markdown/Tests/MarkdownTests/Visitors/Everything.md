# Header

*Emphasized* **strong** `inline code` [link](foo) ![image](foo).

- this
- is
- a
- list

1. eggs
1. milk

> BlockQuote

2. flour
2. sugar

- [x] Combine flour and baking soda.
- [ ] Combine sugar and eggs.

```swift
func foo() {
    let x = 1
}
```

    // Is this real code? Or just fantasy?

This is an <topic://autolink>.

---

<a href="foo.png">
An HTML Block.
</a>

This is some <p>inline html</p>.

line  
break

soft
break

<!-- Copyright (c) 2021 Apple Inc and the Swift Project authors. All Rights Reserved. -->
