{"": {"swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/master.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/ChildIndexPath.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ChildIndexPath.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ChildIndexPath.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ChildIndexPath~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ChildIndexPath.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/DirectiveArgument.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DirectiveArgument.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DirectiveArgument.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DirectiveArgument~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DirectiveArgument.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/Document.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Document.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Document.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Document~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Document.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/LiteralMarkup.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/LiteralMarkup.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/LiteralMarkup.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/LiteralMarkup~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/LiteralMarkup.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/Markup.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Markup.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Markup.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Markup~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Markup.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupChildren.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupChildren.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupChildren.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupChildren~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupChildren.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupData.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupData.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupData.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupData~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupData.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/PlainTextConvertibleMarkup.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/PlainTextConvertibleMarkup.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/PlainTextConvertibleMarkup.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/PlainTextConvertibleMarkup~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/PlainTextConvertibleMarkup.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Base/RawMarkup.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/RawMarkup.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/RawMarkup.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/RawMarkup~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/RawMarkup.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/BlockDirective.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockDirective.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockDirective.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockDirective~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockDirective.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/BlockQuote.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockQuote.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockQuote.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockQuote~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockQuote.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/CustomBlock.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CustomBlock.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CustomBlock.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CustomBlock~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CustomBlock.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenDiscussion.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DoxygenDiscussion.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DoxygenDiscussion.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DoxygenDiscussion~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DoxygenDiscussion.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenNote.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DoxygenNote.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DoxygenNote.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DoxygenNote~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DoxygenNote.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenParameter.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DoxygenParameter.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DoxygenParameter.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DoxygenParameter~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DoxygenParameter.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenReturns.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DoxygenReturns.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DoxygenReturns.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DoxygenReturns~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/DoxygenReturns.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/ListItem.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ListItem.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ListItem.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ListItem~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ListItem.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/OrderedList.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/OrderedList.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/OrderedList.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/OrderedList~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/OrderedList.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/UnorderedList.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/UnorderedList.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/UnorderedList.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/UnorderedList~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/UnorderedList.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Inline Container Blocks/Paragraph.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Paragraph.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Paragraph.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Paragraph~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Paragraph.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/CodeBlock.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CodeBlock.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CodeBlock.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CodeBlock~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CodeBlock.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/HTMLBlock.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/HTMLBlock.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/HTMLBlock.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/HTMLBlock~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/HTMLBlock.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/Heading.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Heading.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Heading.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Heading~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Heading.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/ThematicBreak.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ThematicBreak.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ThematicBreak.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ThematicBreak~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ThematicBreak.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/Table.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Table.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Table.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Table~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Table.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableBody.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableBody.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableBody.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableBody~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableBody.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableCell.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableCell.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableCell.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableCell~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableCell.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableCellContainer.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableCellContainer.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableCellContainer.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableCellContainer~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableCellContainer.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableHead.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableHead.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableHead.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableHead~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableHead.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableRow.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableRow.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableRow.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableRow~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/TableRow.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/Replacement.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Replacement.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Replacement.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Replacement~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Replacement.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/SourceLocation.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/SourceLocation.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/SourceLocation.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/SourceLocation~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/SourceLocation.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Emphasis.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Emphasis.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Emphasis.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Emphasis~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Emphasis.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Image.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Image.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Image.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Image~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Image.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/InlineAttributes.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineAttributes.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineAttributes.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineAttributes~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineAttributes.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Link.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Link.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Link.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Link~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Link.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Strikethrough.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Strikethrough.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Strikethrough.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Strikethrough~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Strikethrough.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Strong.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Strong.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Strong.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Strong~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Strong.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/CustomInline.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CustomInline.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CustomInline.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CustomInline~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CustomInline.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/InlineCode.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineCode.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineCode.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineCode~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineCode.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/InlineHTML.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineHTML.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineHTML.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineHTML~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineHTML.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/LineBreak.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/LineBreak.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/LineBreak.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/LineBreak~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/LineBreak.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/SoftBreak.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/SoftBreak.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/SoftBreak.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/SoftBreak~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/SoftBreak.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/SymbolLink.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/SymbolLink.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/SymbolLink.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/SymbolLink~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/SymbolLink.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/Text.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Text.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Text.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Text~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Text.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Interpretive Nodes/Aside.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Aside.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Aside.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Aside~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/Aside.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/BlockDirectiveParser.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockDirectiveParser.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockDirectiveParser.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockDirectiveParser~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockDirectiveParser.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/CommonMarkConverter.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CommonMarkConverter.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CommonMarkConverter.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CommonMarkConverter~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CommonMarkConverter.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/LazySplitLines.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/LazySplitLines.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/LazySplitLines.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/LazySplitLines~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/LazySplitLines.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/ParseOptions.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ParseOptions.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ParseOptions.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ParseOptions~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ParseOptions.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/RangeAdjuster.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/RangeAdjuster.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/RangeAdjuster.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/RangeAdjuster~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/RangeAdjuster.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Parser/RangerTracker.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/RangerTracker.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/RangerTracker.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/RangerTracker~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/RangerTracker.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Rewriter/MarkupRewriter.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupRewriter.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupRewriter.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupRewriter~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupRewriter.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BasicBlockContainer.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BasicBlockContainer.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BasicBlockContainer.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BasicBlockContainer~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BasicBlockContainer.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BasicInlineContainer.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BasicInlineContainer.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BasicInlineContainer.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BasicInlineContainer~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BasicInlineContainer.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BlockContainer.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockContainer.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockContainer.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockContainer~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockContainer.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BlockMarkup.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockMarkup.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockMarkup.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockMarkup~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/BlockMarkup.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/InlineContainer.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineContainer.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineContainer.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineContainer~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineContainer.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/InlineMarkup.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineMarkup.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineMarkup.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineMarkup~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/InlineMarkup.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/ListItemContainer.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ListItemContainer.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ListItemContainer.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ListItemContainer~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/ListItemContainer.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/AtomicCounter.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/AtomicCounter.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/AtomicCounter.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/AtomicCounter~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/AtomicCounter.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/CharacterExtensions.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CharacterExtensions.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CharacterExtensions.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CharacterExtensions~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CharacterExtensions.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/CollectionExtensions.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CollectionExtensions.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CollectionExtensions.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CollectionExtensions~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/CollectionExtensions.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Utility/StringExtensions.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/StringExtensions.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/StringExtensions.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/StringExtensions~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/StringExtensions.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Visitor/MarkupVisitor.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupVisitor.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupVisitor.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupVisitor~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupVisitor.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/MarkupWalker.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupWalker.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupWalker.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupWalker~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupWalker.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/HTMLFormatter.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/HTMLFormatter.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/HTMLFormatter.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/HTMLFormatter~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/HTMLFormatter.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupFormatter.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupFormatter.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupFormatter.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupFormatter~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupFormatter.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupTreeDumper.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupTreeDumper.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupTreeDumper.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupTreeDumper~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/Markdown.build/MarkupTreeDumper.swiftdeps"}}