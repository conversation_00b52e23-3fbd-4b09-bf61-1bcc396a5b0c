{"": {"swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/master.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Backup.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Backup.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Backup.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Backup~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Backup.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Blob.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Blob.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Blob.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Blob~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Blob.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Aggregation.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection+Aggregation.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection+Aggregation.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection+Aggregation~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection+Aggregation.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Attach.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection+Attach.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection+Attach.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection+Attach~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection+Attach.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection+Pragmas.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection+Pragmas.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection+Pragmas.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection+Pragmas~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection+Pragmas.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Connection.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Errors.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Errors.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Errors.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Errors~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Errors.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Result.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Result.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Result.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Result~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Result.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/SQLiteFeature.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SQLiteFeature.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SQLiteFeature.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SQLiteFeature~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SQLiteFeature.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/SQLiteVersion.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SQLiteVersion.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SQLiteVersion.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SQLiteVersion~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SQLiteVersion.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Statement.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Statement.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Statement.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Statement~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Statement.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/URIQueryParameter.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/URIQueryParameter.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/URIQueryParameter.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/URIQueryParameter~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/URIQueryParameter.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Core/Value.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Value.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Value.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Value~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Value.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/Cipher.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Cipher.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Cipher.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Cipher~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Cipher.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/FTS4.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/FTS4.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/FTS4.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/FTS4~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/FTS4.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/FTS5.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/FTS5.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/FTS5.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/FTS5~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/FTS5.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Extensions/RTree.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/RTree.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/RTree.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/RTree~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/RTree.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Foundation.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Foundation.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Foundation.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Foundation~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Foundation.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Helpers.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Helpers.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Helpers.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Helpers~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Helpers.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/Connection+Schema.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection+Schema.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection+Schema.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection+Schema~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Connection+Schema.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaChanger.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SchemaChanger.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SchemaChanger.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SchemaChanger~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SchemaChanger.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaDefinitions.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SchemaDefinitions.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SchemaDefinitions.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SchemaDefinitions~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SchemaDefinitions.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Schema/SchemaReader.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SchemaReader.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SchemaReader.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SchemaReader~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/SchemaReader.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/AggregateFunctions.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/AggregateFunctions.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/AggregateFunctions.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/AggregateFunctions~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/AggregateFunctions.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Coding.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Coding.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Coding.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Coding~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Coding.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Collation.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Collation.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Collation.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Collation~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Collation.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/CoreFunctions.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/CoreFunctions.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/CoreFunctions.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/CoreFunctions~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/CoreFunctions.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/CustomFunctions.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/CustomFunctions.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/CustomFunctions.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/CustomFunctions~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/CustomFunctions.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/DateAndTimeFunctions.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/DateAndTimeFunctions.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/DateAndTimeFunctions.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/DateAndTimeFunctions~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/DateAndTimeFunctions.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Expression.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Expression.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Expression.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Expression~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Expression.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Operators.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Operators.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Operators.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Operators~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Operators.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Query+with.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Query+with.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Query+with.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Query+with~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Query+with.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Query.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Query.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Query.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Query~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Query.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Schema.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Schema.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Schema.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Schema~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Schema.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/Setter.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Setter.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Setter.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Setter~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/Setter.swiftdeps"}, "/Volumes/ssd/projects/new nexus/.build/index-build/checkouts/SQLite.swift/Sources/SQLite/Typed/WindowFunctions.swift": {"dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/WindowFunctions.d", "object": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/WindowFunctions.swift.o", "swiftmodule": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/WindowFunctions~partial.swiftmodule", "swift-dependencies": "/Volumes/ssd/projects/new nexus/.build/index-build/arm64-apple-macosx/debug/SQLite.build/WindowFunctions.swiftdeps"}}