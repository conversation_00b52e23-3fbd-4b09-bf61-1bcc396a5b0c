import SwiftUI
import KeyboardShortcuts

@main
struct NexusApp: App {
    @NSApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

    var body: some Scene {
        Settings {
            EmptyView()
        }
    }
}

class AppDelegate: NSObject, NSApplicationDelegate {
    var statusItem: NSStatusItem?
    var popover: NSPopover?
    var moduleManager: ModuleManager!
    var searchViewModel: SearchViewModel!
    var configurationManager: ConfigurationManager!
    var dataService: DataService!
    var notesWindowController: NotesWindowController?
    var settingsWindowController: SettingsWindowController?

    func applicationDidFinishLaunching(_ notification: Notification) {
        // Hide the app from the dock
        NSApp.setActivationPolicy(.accessory)

        // Initialize core components
        setupCoreComponents()

        // Setup menu bar
        setupMenuBar()

        // Setup global hotkeys
        setupGlobalHotkeys()

        // Activate modules
        Task {
            await moduleManager.activateModules()
        }
    }

    func applicationWillTerminate(_ notification: Notification) {
        Task {
            await moduleManager.deactivateModules()
        }
    }

    private func setupCoreComponents() {
        configurationManager = ConfigurationManager()
        dataService = DataService()
        moduleManager = ModuleManager()

        // Connect configuration manager to module manager
        moduleManager.setConfigurationManager(configurationManager)

        searchViewModel = SearchViewModel(moduleManager: moduleManager)

        // Register modules
        registerModules()

        // Setup windows
        setupNotesWindow()
        setupSettingsWindow()
    }

    private func registerModules() {
        let config = configurationManager.configuration

        // Register Notes Hub module
        let notesHubModule = NotesHubModule()
        notesHubModule.configure(with: ["dataService": dataService!])
        moduleManager.register(notesHubModule)

        // Register Smart Calculator module
        let calculatorModule = SmartCalculatorModule()
        calculatorModule.configure(with: config.calculatorSettings)
        moduleManager.register(calculatorModule)

        // Register Clipboard Manager module
        let clipboardModule = ClipboardManagerModule()
        clipboardModule.configure(with: config.clipboardSettings)
        moduleManager.register(clipboardModule)

        // Register Quick Launcher module
        let launcherModule = QuickLauncherModule()
        moduleManager.register(launcherModule)
    }

    private func setupNotesWindow() {
        notesWindowController = NotesWindowController(dataService: dataService)
    }

    private func setupSettingsWindow() {
        settingsWindowController = SettingsWindowController(
            configurationManager: configurationManager,
            moduleManager: moduleManager
        )
    }

    private func setupMenuBar() {
        // Create status item
        statusItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.variableLength)

        if let button = statusItem?.button {
            button.image = NSImage(systemSymbolName: "command.circle", accessibilityDescription: "Nexus")
            button.action = #selector(togglePopover)
            button.target = self

            // Add right-click menu
            let menu = NSMenu()
            menu.addItem(NSMenuItem(title: "Open Nexus", action: #selector(togglePopover), keyEquivalent: ""))
            menu.addItem(NSMenuItem.separator())
            menu.addItem(NSMenuItem(title: "Settings...", action: #selector(openSettings), keyEquivalent: ","))
            menu.addItem(NSMenuItem.separator())
            menu.addItem(NSMenuItem(title: "Quit Nexus", action: #selector(NSApplication.terminate(_:)), keyEquivalent: "q"))

            button.menu = menu
        }

        // Create popover
        popover = NSPopover()
        popover?.contentSize = NSSize(width: 400, height: 300)
        popover?.behavior = .applicationDefined
        popover?.appearance = NSAppearance(named: .vibrantLight)

        let contentView = ContentView()
            .environment(\.searchViewModel, searchViewModel)
            .environment(\.moduleManager, moduleManager)
            .environment(\.configurationManager, configurationManager)
            .environment(\.dataService, dataService)

        popover?.contentViewController = NSHostingController(rootView: contentView)
    }

    private func setupGlobalHotkeys() {
        // Register the main toggle hotkey
        KeyboardShortcuts.onKeyUp(for: .toggleNexus) { [weak self] in
            self?.togglePopover()
        }

        // Register module-specific hotkeys
        KeyboardShortcuts.onKeyUp(for: .calculatorModule) { [weak self] in
            self?.openModuleDirectly("smart-calculator")
        }

        KeyboardShortcuts.onKeyUp(for: .clipboardModule) { [weak self] in
            self?.openModuleDirectly("clipboard-manager")
        }

        KeyboardShortcuts.onKeyUp(for: .notesModule) { [weak self] in
            self?.openModuleDirectly("notes-hub")
        }

        KeyboardShortcuts.onKeyUp(for: .launcherModule) { [weak self] in
            self?.openModuleDirectly("quick-launcher")
        }
    }

    @objc func togglePopover() {
        guard let popover = popover else { return }

        if popover.isShown {
            popover.performClose(nil)
        } else {
            if let button = statusItem?.button {
                popover.show(relativeTo: button.bounds, of: button, preferredEdge: NSRectEdge.minY)

                // Focus the search field when popover opens
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    if let contentViewController = popover.contentViewController as? NSHostingController<ContentView> {
                        contentViewController.view.window?.makeFirstResponder(nil)
                    }
                }
            }
        }
    }

    @objc func openSettings() {
        settingsWindowController?.showSettings()
    }

    func openModuleDirectly(_ moduleId: String) {
        // Check if module is enabled
        guard moduleManager.isModuleEnabled(moduleId) else { return }

        // For now, just open the main popover
        // In the future, this could open module-specific interfaces
        togglePopover()
    }
}

// MARK: - Keyboard Shortcut Extensions

extension KeyboardShortcuts.Name {
    static let toggleNexus = Self("toggleNexus", default: .init(.space, modifiers: [.command, .shift]))
    static let calculatorModule = Self("calculatorModule")
    static let clipboardModule = Self("clipboardModule")
    static let notesModule = Self("notesModule")
    static let launcherModule = Self("launcherModule")
}


