import Foundation
import CryptoKit

/// Manages data encryption and decryption using AES-GCM
class EncryptionManager {
    private var masterKey: SymmetricKey?
    private let nonceSize = 12 // AES-GCM nonce size
    
    /// Configure encryption manager with master key
    func configure(with key: Sym<PERSON><PERSON><PERSON>) {
        self.masterKey = key
    }
    
    /// Encrypt data using AES-GCM
    func encrypt(_ data: Data) throws -> Data {
        guard let masterKey = masterKey else {
            throw SecurityError.keyGenerationFailed
        }
        
        do {
            let sealedBox = try AES.GCM.seal(data, using: masterKey)
            
            // Combine nonce and ciphertext for storage
            guard let combined = sealedBox.combined else {
                throw SecurityError.encryptionFailed
            }
            
            return combined
        } catch {
            throw SecurityError.encryptionFailed
        }
    }
    
    /// Decrypt data using AES-GCM
    func decrypt(_ encryptedData: Data) throws -> Data {
        guard let masterKey = masterKey else {
            throw SecurityError.keyGenerationFailed
        }
        
        do {
            let sealedBox = try AES.GCM.SealedBox(combined: encryptedData)
            let decryptedData = try AES.GCM.open(sealedBox, using: master<PERSON><PERSON>)
            
            return decryptedData
        } catch {
            throw SecurityError.decryptionFailed
        }
    }
    
    /// Encrypt string data
    func encryptString(_ string: String) throws -> Data {
        guard let data = string.data(using: .utf8) else {
            throw SecurityError.invalidData
        }
        return try encrypt(data)
    }
    
    /// Decrypt to string
    func decryptToString(_ encryptedData: Data) throws -> String {
        let data = try decrypt(encryptedData)
        guard let string = String(data: data, encoding: .utf8) else {
            throw SecurityError.invalidData
        }
        return string
    }
    
    /// Generate a new symmetric key
    static func generateKey() -> SymmetricKey {
        return SymmetricKey(size: .bits256)
    }
    
    /// Derive key from password using PBKDF2
    static func deriveKey(from password: String, salt: Data) throws -> SymmetricKey {
        guard let passwordData = password.data(using: .utf8) else {
            throw SecurityError.invalidData
        }
        
        // Use PBKDF2 with SHA256, 100,000 iterations
        let derivedKey = try HKDF<SHA256>.deriveKey(
            inputKeyMaterial: SymmetricKey(data: passwordData),
            salt: salt,
            outputByteCount: 32
        )
        
        return derivedKey
    }
    
    /// Generate random salt for key derivation
    static func generateSalt() -> Data {
        var salt = Data(count: 32)
        _ = salt.withUnsafeMutableBytes { bytes in
            SecRandomCopyBytes(kSecRandomDefault, 32, bytes.bindMemory(to: UInt8.self).baseAddress!)
        }
        return salt
    }
    
    /// Encrypt data with additional authenticated data (AAD)
    func encryptWithAAD(_ data: Data, additionalData: Data) throws -> Data {
        guard let masterKey = masterKey else {
            throw SecurityError.keyGenerationFailed
        }
        
        do {
            let sealedBox = try AES.GCM.seal(data, using: masterKey, authenticating: additionalData)
            
            guard let combined = sealedBox.combined else {
                throw SecurityError.encryptionFailed
            }
            
            return combined
        } catch {
            throw SecurityError.encryptionFailed
        }
    }
    
    /// Decrypt data with additional authenticated data (AAD)
    func decryptWithAAD(_ encryptedData: Data, additionalData: Data) throws -> Data {
        guard let masterKey = masterKey else {
            throw SecurityError.keyGenerationFailed
        }
        
        do {
            let sealedBox = try AES.GCM.SealedBox(combined: encryptedData)
            let decryptedData = try AES.GCM.open(sealedBox, using: masterKey, authenticating: additionalData)
            
            return decryptedData
        } catch {
            throw SecurityError.decryptionFailed
        }
    }
    
    /// Create hash of data for integrity checking
    func createHash(_ data: Data) -> Data {
        return Data(SHA256.hash(data: data))
    }
    
    /// Verify data integrity using hash
    func verifyHash(_ data: Data, expectedHash: Data) -> Bool {
        let actualHash = createHash(data)
        return actualHash == expectedHash
    }
    
    /// Securely compare two data objects (constant time)
    func secureCompare(_ data1: Data, _ data2: Data) -> Bool {
        guard data1.count == data2.count else { return false }
        
        var result: UInt8 = 0
        for i in 0..<data1.count {
            result |= data1[i] ^ data2[i]
        }
        
        return result == 0
    }
    
    /// Reset encryption manager
    func reset() {
        masterKey = nil
    }
    
    /// Check if encryption is configured
    var isConfigured: Bool {
        return masterKey != nil
    }
}

/// Encrypted data container with metadata
struct EncryptedContainer: Codable {
    let encryptedData: Data
    let timestamp: Date
    let version: Int
    let metadata: [String: String]
    
    init(encryptedData: Data, metadata: [String: String] = [:]) {
        self.encryptedData = encryptedData
        self.timestamp = Date()
        self.version = 1
        self.metadata = metadata
    }
}

/// Encryption configuration
struct EncryptionConfig {
    static let currentVersion = 1
    static let algorithm = "AES-GCM-256"
    static let nonceSize = 12
    static let tagSize = 16
}
