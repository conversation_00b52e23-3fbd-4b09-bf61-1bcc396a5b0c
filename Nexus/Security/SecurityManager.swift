import Foundation
import CryptoKit
import Security

/// Manages security and encryption for the Nexus application
@Observable
class SecurityManager {
    private let keychain = KeychainManager()
    private let encryptionManager = EncryptionManager()
    private var settings: SecuritySettings = .default
    
    var isEncryptionEnabled: Bool { settings.enableDataEncryption }
    var isClipboardEncryptionEnabled: Bool { settings.enableClipboardEncryption }
    var isNotesEncryptionEnabled: Bool { settings.enableNotesEncryption }
    
    init() {
        // Initialize with default settings
        // Settings will be updated when ConfigurationManager loads
    }
    
    /// Configure security manager with settings
    func configure(with settings: SecuritySettings) {
        self.settings = settings
        
        // Initialize encryption if enabled
        if settings.enableDataEncryption {
            Task {
                await initializeEncryption()
            }
        }
    }
    
    /// Initialize encryption keys
    private func initializeEncryption() async {
        do {
            // Check if master key exists, create if not
            if !keychain.masterKeyExists() {
                let masterKey = SymmetricKey(size: .bits256)
                try keychain.storeMaster<PERSON><PERSON>(master<PERSON><PERSON>)
                print("Created new master encryption key")
            }
            
            // Initialize encryption manager
            if let masterKey = try keychain.retrieveMasterKey() {
                encryptionManager.configure(with: master<PERSON>ey)
                print("Encryption initialized successfully")
            }
        } catch {
            print("Failed to initialize encryption: \(error)")
        }
    }
    
    /// Encrypt data if encryption is enabled
    func encryptData(_ data: Data) throws -> Data {
        guard settings.enableDataEncryption else { return data }
        return try encryptionManager.encrypt(data)
    }
    
    /// Decrypt data if encryption is enabled
    func decryptData(_ encryptedData: Data) throws -> Data {
        guard settings.enableDataEncryption else { return encryptedData }
        return try encryptionManager.decrypt(encryptedData)
    }
    
    /// Encrypt string content
    func encryptString(_ string: String) throws -> Data {
        guard let data = string.data(using: .utf8) else {
            throw SecurityError.invalidData
        }
        return try encryptData(data)
    }
    
    /// Decrypt string content
    func decryptString(_ encryptedData: Data) throws -> String {
        let data = try decryptData(encryptedData)
        guard let string = String(data: data, encoding: .utf8) else {
            throw SecurityError.invalidData
        }
        return string
    }
    
    /// Encrypt clipboard content if clipboard encryption is enabled
    func encryptClipboardContent(_ content: String) throws -> Data {
        guard settings.enableClipboardEncryption else {
            return content.data(using: .utf8) ?? Data()
        }
        return try encryptString(content)
    }
    
    /// Decrypt clipboard content if clipboard encryption is enabled
    func decryptClipboardContent(_ encryptedData: Data) throws -> String {
        guard settings.enableClipboardEncryption else {
            return String(data: encryptedData, encoding: .utf8) ?? ""
        }
        return try decryptString(encryptedData)
    }
    
    /// Encrypt note content if notes encryption is enabled
    func encryptNoteContent(_ content: String) throws -> Data {
        guard settings.enableNotesEncryption else {
            return content.data(using: .utf8) ?? Data()
        }
        return try encryptString(content)
    }
    
    /// Decrypt note content if notes encryption is enabled
    func decryptNoteContent(_ encryptedData: Data) throws -> String {
        guard settings.enableNotesEncryption else {
            return String(data: encryptedData, encoding: .utf8) ?? ""
        }
        return try decryptString(encryptedData)
    }
    
    /// Securely erase data if secure erase is enabled
    func secureErase(_ data: inout Data) {
        guard settings.enableSecureErase else { return }
        
        // Overwrite with random data multiple times
        for _ in 0..<3 {
            let randomData = Data((0..<data.count).map { _ in UInt8.random(in: 0...255) })
            data = randomData
        }
        
        // Final overwrite with zeros
        data = Data(repeating: 0, count: data.count)
    }
    
    /// Check if data should be retained based on retention policy
    func shouldRetainData(createdAt: Date) -> Bool {
        guard settings.dataRetentionDays > 0 else { return true }
        
        let retentionPeriod = TimeInterval(settings.dataRetentionDays * 24 * 60 * 60)
        let expirationDate = createdAt.addingTimeInterval(retentionPeriod)
        
        return Date() < expirationDate
    }
    
    /// Clean up expired data
    func cleanupExpiredData() async {
        // This will be called by individual modules to clean their data
        print("Cleaning up expired data based on retention policy")
    }
    
    /// Reset all security settings and keys
    func resetSecurity() throws {
        try keychain.deleteMasterKey()
        encryptionManager.reset()
        print("Security settings and keys reset")
    }
}

/// Security-related errors
enum SecurityError: Error, LocalizedError {
    case encryptionFailed
    case decryptionFailed
    case keyGenerationFailed
    case keychainError
    case invalidData
    case authenticationRequired
    
    var errorDescription: String? {
        switch self {
        case .encryptionFailed:
            return "Failed to encrypt data"
        case .decryptionFailed:
            return "Failed to decrypt data"
        case .keyGenerationFailed:
            return "Failed to generate encryption key"
        case .keychainError:
            return "Keychain operation failed"
        case .invalidData:
            return "Invalid data format"
        case .authenticationRequired:
            return "Authentication required"
        }
    }
}
