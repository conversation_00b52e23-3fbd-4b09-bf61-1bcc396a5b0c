import Foundation

/// Manages application configuration persistence
@Observable
class ConfigurationManager {
    var configuration: AppConfiguration
    
    private let configurationURL: URL
    private let fileManager = FileManager.default
    
    init() {
        // Get application support directory
        let appSupportURL = fileManager.urls(for: .applicationSupportDirectory, 
                                           in: .userDomainMask).first!
        let nexusDirectory = appSupportURL.appendingPathComponent("Nexus")
        
        // Create directory if it doesn't exist
        try? fileManager.createDirectory(at: nexusDirectory, 
                                       withIntermediateDirectories: true)
        
        configurationURL = nexusDirectory.appendingPathComponent("configuration.json")
        
        // Load existing configuration or use default
        if let data = try? Data(contentsOf: configurationURL),
           let config = try? JSONDecoder().decode(AppConfiguration.self, from: data) {
            self.configuration = config
        } else {
            self.configuration = AppConfiguration.default
            saveConfiguration()
        }
    }
    
    /// Save current configuration to disk
    func saveConfiguration() {
        do {
            let data = try JSONEncoder().encode(configuration)
            try data.write(to: configurationURL)
        } catch {
            print("Failed to save configuration: \(error)")
        }
    }
    
    /// Update editor settings
    func updateEditorSettings(_ settings: EditorSettings) {
        configuration = AppConfiguration(
            hotkeys: configuration.hotkeys,
            editorSettings: settings,
            moduleSettings: configuration.moduleSettings,
            securitySettings: configuration.securitySettings
        )
        saveConfiguration()
    }
    
    /// Update module settings
    func updateModuleSettings(for moduleId: String, settings: ModuleSettings) {
        var moduleSettings = configuration.moduleSettings
        moduleSettings[moduleId] = settings

        configuration = AppConfiguration(
            hotkeys: configuration.hotkeys,
            editorSettings: configuration.editorSettings,
            moduleSettings: moduleSettings,
            securitySettings: configuration.securitySettings
        )
        saveConfiguration()
    }

    /// Update security settings
    func updateSecuritySettings(_ settings: SecuritySettings) {
        configuration = AppConfiguration(
            hotkeys: configuration.hotkeys,
            editorSettings: configuration.editorSettings,
            moduleSettings: configuration.moduleSettings,
            securitySettings: settings
        )
        saveConfiguration()
    }

    /// Get settings for a specific module
    func moduleSettings(for moduleId: String) -> ModuleSettings {
        return configuration.moduleSettings[moduleId] ?? ModuleSettings()
    }

    // MARK: - Typed Module Settings Updates

    /// Update Calculator module settings
    func updateCalculatorSettings(_ settings: CalculatorModuleSettings) {
        if let data = try? JSONEncoder().encode(settings),
           let dict = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
            updateModuleSettings(for: "smart-calculator", settings: ModuleSettings(dict))
        }
    }

    /// Update Clipboard module settings
    func updateClipboardSettings(_ settings: ClipboardModuleSettings) {
        if let data = try? JSONEncoder().encode(settings),
           let dict = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
            updateModuleSettings(for: "clipboard-manager", settings: ModuleSettings(dict))
        }
    }

    /// Update Notes Hub module settings
    func updateNotesHubSettings(_ settings: NotesHubModuleSettings) {
        if let data = try? JSONEncoder().encode(settings),
           let dict = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
            updateModuleSettings(for: "notes-hub", settings: ModuleSettings(dict))
        }
    }

    /// Update Quick Launcher module settings
    func updateQuickLauncherSettings(_ settings: QuickLauncherModuleSettings) {
        if let data = try? JSONEncoder().encode(settings),
           let dict = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
            updateModuleSettings(for: "quick-launcher", settings: ModuleSettings(dict))
        }
    }

    /// Toggle module enabled state
    func toggleModuleEnabled(_ moduleId: String) {
        switch moduleId {
        case "smart-calculator":
            let settings = configuration.calculatorSettings
            let newSettings = CalculatorModuleSettings(
                isEnabled: !settings.isEnabled,
                decimalPlaces: settings.decimalPlaces,
                angleUnit: settings.angleUnit,
                enableUnitConversions: settings.enableUnitConversions,
                enableCurrencyConversions: settings.enableCurrencyConversions,
                keyboardShortcut: settings.keyboardShortcut
            )
            updateCalculatorSettings(newSettings)

        case "clipboard-manager":
            let settings = configuration.clipboardSettings
            let newSettings = ClipboardModuleSettings(
                isEnabled: !settings.isEnabled,
                maxHistoryItems: settings.maxHistoryItems,
                enableImageCapture: settings.enableImageCapture,
                enableFileCapture: settings.enableFileCapture,
                autoCleanupDays: settings.autoCleanupDays,
                keyboardShortcut: settings.keyboardShortcut
            )
            updateClipboardSettings(newSettings)

        case "notes-hub":
            let settings = configuration.notesHubSettings
            let newSettings = NotesHubModuleSettings(
                isEnabled: !settings.isEnabled,
                defaultNoteFormat: settings.defaultNoteFormat,
                enableAutoSave: settings.enableAutoSave,
                autoSaveInterval: settings.autoSaveInterval,
                showPreviewInSearch: settings.showPreviewInSearch,
                keyboardShortcut: settings.keyboardShortcut
            )
            updateNotesHubSettings(newSettings)

        case "quick-launcher":
            let settings = configuration.quickLauncherSettings
            let newSettings = QuickLauncherModuleSettings(
                isEnabled: !settings.isEnabled,
                indexSystemApps: settings.indexSystemApps,
                indexUserApps: settings.indexUserApps,
                enableFuzzySearch: settings.enableFuzzySearch,
                maxRecentItems: settings.maxRecentItems,
                keyboardShortcut: settings.keyboardShortcut
            )
            updateQuickLauncherSettings(newSettings)

        default:
            break
        }
    }
    
    /// Reset configuration to defaults
    func resetToDefaults() {
        configuration = AppConfiguration.default
        saveConfiguration()
    }
}
