import Foundation
import SwiftUI

/// Smart Calculator module for mathematical expressions and unit conversions
class SmartCalculatorModule: NexusModule {
    let identifier = "smart-calculator"
    let displayName = "Smart Calculator"
    var isEnabled = true
    let iconName = "function"

    private let calculator = CalculatorEngine()
    private var settings: CalculatorModuleSettings = .default
    
    func activate() async throws {
        print("Smart Calculator module activated")
    }

    func deactivate() async {
        print("Smart Calculator module deactivated")
    }

    /// Configure the module with settings
    func configure(with settings: CalculatorModuleSettings) {
        self.settings = settings
        calculator.configure(with: settings)
    }
    
    func search(_ query: String) async -> [SearchResult] {
        guard !query.isEmpty else { return [] }
        
        var results: [SearchResult] = []
        
        // Try to evaluate as mathematical expression
        if let mathResult = calculator.evaluateExpression(query) {
            let formattedResult = String(format: "%.\(settings.decimalPlaces)f", mathResult)
            results.append(SearchResult(
                title: "\(query) = \(formattedResult)",
                subtitle: "Mathematical calculation",
                iconName: "equal.circle",
                moduleIdentifier: identifier,
                action: .copyToClipboard(text: formattedResult)
            ))
        }

        // Try unit conversions (if enabled)
        if settings.enableUnitConversions, let conversionResults = calculator.performUnitConversion(query) {
            for conversion in conversionResults {
                results.append(SearchResult(
                    title: conversion.result,
                    subtitle: conversion.description,
                    iconName: "arrow.left.arrow.right.circle",
                    moduleIdentifier: identifier,
                    action: .copyToClipboard(text: conversion.result)
                ))
            }
        }

        // Try currency conversions (if enabled)
        if settings.enableCurrencyConversions, let currencyResult = calculator.performCurrencyConversion(query) {
            results.append(SearchResult(
                title: currencyResult.result,
                subtitle: currencyResult.description,
                iconName: "dollarsign.circle",
                moduleIdentifier: identifier,
                action: .copyToClipboard(text: currencyResult.result)
            ))
        }
        
        return results
    }
}

/// Calculator engine for mathematical operations and conversions
class CalculatorEngine {
    private var settings: CalculatorModuleSettings = .default

    /// Configure the engine with settings
    func configure(with settings: CalculatorModuleSettings) {
        self.settings = settings
    }

    /// Evaluate mathematical expressions
    func evaluateExpression(_ expression: String) -> Double? {
        // Clean the expression
        let cleanExpression = expression
            .replacingOccurrences(of: " ", with: "")
            .replacingOccurrences(of: "×", with: "*")
            .replacingOccurrences(of: "÷", with: "/")
        
        // Use NSExpression for safe evaluation
        let nsExpression = NSExpression(format: cleanExpression)
        
        do {
            if let result = try nsExpression.expressionValue(with: nil, context: nil) as? NSNumber {
                return result.doubleValue
            }
        } catch {
            // Try some common mathematical functions
            return evaluateAdvancedExpression(cleanExpression)
        }
        
        return nil
    }
    
    /// Handle advanced mathematical functions
    private func evaluateAdvancedExpression(_ expression: String) -> Double? {
        let lowercased = expression.lowercased()
        
        // Square root
        if lowercased.hasPrefix("sqrt(") && lowercased.hasSuffix(")") {
            let numberStr = String(lowercased.dropFirst(5).dropLast(1))
            if let number = Double(numberStr) {
                return sqrt(number)
            }
        }
        
        // Power function
        if lowercased.contains("^") {
            let components = lowercased.components(separatedBy: "^")
            if components.count == 2,
               let base = Double(components[0]),
               let exponent = Double(components[1]) {
                return pow(base, exponent)
            }
        }
        
        // Trigonometric functions
        if lowercased.hasPrefix("sin(") && lowercased.hasSuffix(")") {
            let numberStr = String(lowercased.dropFirst(4).dropLast(1))
            if let number = Double(numberStr) {
                let radians = settings.angleUnit == .degrees ? number * .pi / 180 : number
                return sin(radians)
            }
        }

        if lowercased.hasPrefix("cos(") && lowercased.hasSuffix(")") {
            let numberStr = String(lowercased.dropFirst(4).dropLast(1))
            if let number = Double(numberStr) {
                let radians = settings.angleUnit == .degrees ? number * .pi / 180 : number
                return cos(radians)
            }
        }

        if lowercased.hasPrefix("tan(") && lowercased.hasSuffix(")") {
            let numberStr = String(lowercased.dropFirst(4).dropLast(1))
            if let number = Double(numberStr) {
                let radians = settings.angleUnit == .degrees ? number * .pi / 180 : number
                return tan(radians)
            }
        }
        
        return nil
    }
    
    /// Perform unit conversions
    func performUnitConversion(_ query: String) -> [ConversionResult]? {
        let lowercased = query.lowercased()
        var results: [ConversionResult] = []
        
        // Temperature conversions
        if let tempResult = convertTemperature(lowercased) {
            results.append(contentsOf: tempResult)
        }
        
        // Length conversions
        if let lengthResult = convertLength(lowercased) {
            results.append(contentsOf: lengthResult)
        }
        
        // Weight conversions
        if let weightResult = convertWeight(lowercased) {
            results.append(contentsOf: weightResult)
        }
        
        return results.isEmpty ? nil : results
    }
    
    private func convertTemperature(_ query: String) -> [ConversionResult]? {
        // Pattern: "25 celsius to fahrenheit" or "25c to f"
        let patterns = [
            #"(\d+(?:\.\d+)?)\s*(?:degrees?\s*)?(?:celsius|c)\s*(?:to|in)\s*(?:fahrenheit|f)"#,
            #"(\d+(?:\.\d+)?)\s*(?:degrees?\s*)?(?:fahrenheit|f)\s*(?:to|in)\s*(?:celsius|c)"#,
            #"(\d+(?:\.\d+)?)\s*(?:degrees?\s*)?(?:celsius|c)\s*(?:to|in)\s*(?:kelvin|k)"#
        ]
        
        for pattern in patterns {
            if let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive),
               let match = regex.firstMatch(in: query, range: NSRange(query.startIndex..., in: query)),
               let valueRange = Range(match.range(at: 1), in: query),
               let value = Double(String(query[valueRange])) {
                
                var results: [ConversionResult] = []
                
                if query.contains("celsius") || query.contains(" c ") {
                    if query.contains("fahrenheit") || query.contains(" f ") {
                        let fahrenheit = (value * 9/5) + 32
                        results.append(ConversionResult(
                            result: String(format: "%.1f°F", fahrenheit),
                            description: "Celsius to Fahrenheit"
                        ))
                    } else if query.contains("kelvin") || query.contains(" k ") {
                        let kelvin = value + 273.15
                        results.append(ConversionResult(
                            result: String(format: "%.1f K", kelvin),
                            description: "Celsius to Kelvin"
                        ))
                    }
                } else if query.contains("fahrenheit") || query.contains(" f ") {
                    let celsius = (value - 32) * 5/9
                    results.append(ConversionResult(
                        result: String(format: "%.1f°C", celsius),
                        description: "Fahrenheit to Celsius"
                    ))
                }
                
                return results.isEmpty ? nil : results
            }
        }
        
        return nil
    }
    
    private func convertLength(_ query: String) -> [ConversionResult]? {
        // Pattern: "5 feet to meters" or "100 cm to inches"
        let lengthUnits = [
            ("feet", "ft", 0.3048),
            ("meters", "m", 1.0),
            ("centimeters", "cm", 0.01),
            ("inches", "in", 0.0254),
            ("yards", "yd", 0.9144),
            ("kilometers", "km", 1000.0),
            ("miles", "mi", 1609.34)
        ]
        
        for fromUnit in lengthUnits {
            for toUnit in lengthUnits {
                if fromUnit.0 != toUnit.0 {
                    let pattern = #"(\d+(?:\.\d+)?)\s*(?:\b\#(fromUnit.0)\b|\b\#(fromUnit.1)\b)\s*(?:to|in)\s*(?:\b\#(toUnit.0)\b|\b\#(toUnit.1)\b)"#
                    
                    if let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive),
                       let match = regex.firstMatch(in: query, range: NSRange(query.startIndex..., in: query)),
                       let valueRange = Range(match.range(at: 1), in: query),
                       let value = Double(String(query[valueRange])) {
                        
                        let meters = value * fromUnit.2
                        let result = meters / toUnit.2
                        
                        return [ConversionResult(
                            result: String(format: "%.2f %@", result, toUnit.1),
                            description: "\(fromUnit.0.capitalized) to \(toUnit.0.capitalized)"
                        )]
                    }
                }
            }
        }
        
        return nil
    }
    
    private func convertWeight(_ query: String) -> [ConversionResult]? {
        // Pattern: "5 pounds to kg" or "100 grams to ounces"
        let weightUnits = [
            ("pounds", "lbs", 0.453592),
            ("kilograms", "kg", 1.0),
            ("grams", "g", 0.001),
            ("ounces", "oz", 0.0283495)
        ]
        
        for fromUnit in weightUnits {
            for toUnit in weightUnits {
                if fromUnit.0 != toUnit.0 {
                    let pattern = #"(\d+(?:\.\d+)?)\s*(?:\b\#(fromUnit.0)\b|\b\#(fromUnit.1)\b)\s*(?:to|in)\s*(?:\b\#(toUnit.0)\b|\b\#(toUnit.1)\b)"#
                    
                    if let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive),
                       let match = regex.firstMatch(in: query, range: NSRange(query.startIndex..., in: query)),
                       let valueRange = Range(match.range(at: 1), in: query),
                       let value = Double(String(query[valueRange])) {
                        
                        let kilograms = value * fromUnit.2
                        let result = kilograms / toUnit.2
                        
                        return [ConversionResult(
                            result: String(format: "%.2f %@", result, toUnit.1),
                            description: "\(fromUnit.0.capitalized) to \(toUnit.0.capitalized)"
                        )]
                    }
                }
            }
        }
        
        return nil
    }
    
    /// Perform currency conversions (mock implementation)
    func performCurrencyConversion(_ query: String) -> ConversionResult? {
        // Mock currency conversion - in a real app, this would use a currency API
        let pattern = #"(\d+(?:\.\d+)?)\s*(?:usd|dollars?)\s*(?:to|in)\s*(?:eur|euros?)"#
        
        if let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive),
           let match = regex.firstMatch(in: query, range: NSRange(query.startIndex..., in: query)),
           let valueRange = Range(match.range(at: 1), in: query),
           let value = Double(String(query[valueRange])) {
            
            let mockExchangeRate = 0.85 // Mock EUR/USD rate
            let result = value * mockExchangeRate
            
            return ConversionResult(
                result: String(format: "€%.2f", result),
                description: "USD to EUR (mock rate)"
            )
        }
        
        return nil
    }
}

/// Result of a unit conversion
struct ConversionResult {
    let result: String
    let description: String
}
