import Foundation
import SwiftUI

/// Notes Hub module for managing notes and folders
class NotesHubModule: NexusModule {
    let identifier = "notes-hub"
    let displayName = "Notes Hub"
    var isEnabled = true
    let iconName = "note.text"

    private var dataService: DataService?

    func activate() async throws {
        // Module activation logic
        print("Notes Hub module activated")
    }

    func deactivate() async {
        // Module deactivation logic
        print("Notes Hub module deactivated")
    }

    func search(_ query: String) async -> [SearchResult] {
        guard let dataService = dataService else { return [] }

        let notes = await dataService.searchNotes(query: query)

        return notes.map { note in
            SearchResult(
                title: note.title,
                subtitle: String(note.content.prefix(100)),
                iconName: "note.text",
                moduleIdentifier: identifier,
                action: .openNote(id: note.id)
            )
        }
    }

    func configure(with services: [String: Any]) {
        if let dataService = services["dataService"] as? DataService {
            self.dataService = dataService
        }
    }

    /// Open a specific note by ID
    func openNote(id: UUID) {
        guard let dataService = dataService else { return }

        // Find the note and trigger a notification to open it
        if let note = dataService.notes.first(where: { $0.id == id }) {
            NotificationCenter.default.post(
                name: .openNoteNotification,
                object: note
            )
        }
    }
    
    /// Create a new note
    func createNote(content: String = "") -> Note? {
        return dataService?.createNote(content: content)
    }
    
    /// Get recent notes
    func getRecentNotes() -> [Note] {
        return dataService?.getRecentNotes() ?? []
    }
    
    /// Get favorite notes
    func getFavoriteNotes() -> [Note] {
        return dataService?.getFavoriteNotes() ?? []
    }
    
    /// Toggle favorite status
    func toggleFavorite(_ note: Note) {
        dataService?.toggleFavorite(note)
    }
    
    /// Delete a note
    func deleteNote(_ note: Note) {
        dataService?.deleteNote(note)
    }
    
    /// Export note
    func exportNote(_ note: Note) -> URL? {
        return dataService?.exportNote(note)
    }
    
    /// Get statistics
    func getStatistics() -> (totalNotes: Int, favoriteNotes: Int, archivedNotes: Int, totalFolders: Int) {
        return dataService?.getStatistics() ?? (0, 0, 0, 0)
    }
}

/// SwiftUI view for Notes Hub interface
struct NotesHubView: View {
    @Environment(\.dataService) private var dataService
    @State private var selectedNote: Note?
    @State private var showingNewNote = false
    
    var body: some View {
        NavigationSplitView {
            // Sidebar with notes list
            List(dataService.notes, id: \.id, selection: $selectedNote) { note in
                VStack(alignment: .leading, spacing: 4) {
                    Text(note.title)
                        .font(.headline)
                        .lineLimit(1)
                    
                    Text(note.content)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                    
                    HStack {
                        Text(note.modifiedAt, style: .relative)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        if note.isFavorite {
                            Image(systemName: "heart.fill")
                                .foregroundColor(.red)
                                .font(.caption)
                        }
                        
                        if note.isArchived {
                            Image(systemName: "archivebox.fill")
                                .foregroundColor(.orange)
                                .font(.caption)
                        }
                    }
                }
                .padding(.vertical, 2)
            }
            .navigationTitle("Notes")
            .toolbar {
                ToolbarItem(placement: .primaryAction) {
                    Button(action: { showingNewNote = true }) {
                        Image(systemName: "plus")
                    }
                }
            }
        } detail: {
            // Detail view for selected note
            if let note = selectedNote {
                NoteDetailView(note: note)
            } else {
                Text("Select a note to view")
                    .foregroundColor(.secondary)
            }
        }
        .sheet(isPresented: $showingNewNote) {
            NewNoteView()
        }
    }
}

/// Detail view for editing a note
struct NoteDetailView: View {
    @Environment(\.dataService) private var dataService
    @State private var note: Note
    @State private var editedContent: String
    
    init(note: Note) {
        self._note = State(initialValue: note)
        self._editedContent = State(initialValue: note.content)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Note metadata
            HStack {
                VStack(alignment: .leading) {
                    Text(note.title)
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text("Modified \(note.modifiedAt, style: .relative)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                HStack {
                    Button(action: { dataService.toggleFavorite(note) }) {
                        Image(systemName: note.isFavorite ? "heart.fill" : "heart")
                            .foregroundColor(note.isFavorite ? .red : .secondary)
                    }
                    
                    Button(action: { dataService.toggleArchive(note) }) {
                        Image(systemName: note.isArchived ? "archivebox.fill" : "archivebox")
                            .foregroundColor(note.isArchived ? .orange : .secondary)
                    }
                }
            }
            
            Divider()
            
            // Note content editor
            TextEditor(text: $editedContent)
                .font(.body)
                .onChange(of: editedContent) { _, newValue in
                    var updatedNote = note
                    updatedNote.content = newValue
                    updatedNote.modifiedAt = Date()
                    dataService.updateNote(updatedNote)
                    note = updatedNote
                }
        }
        .padding()
    }
}

/// View for creating a new note
struct NewNoteView: View {
    @Environment(\.dataService) private var dataService
    @Environment(\.dismiss) private var dismiss
    @State private var content = ""
    
    var body: some View {
        NavigationView {
            VStack {
                TextEditor(text: $content)
                    .font(.body)
                    .padding()
            }
            .navigationTitle("New Note")
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .confirmationAction) {
                    Button("Save") {
                        _ = dataService.createNote(content: content)
                        dismiss()
                    }
                    .disabled(content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
        }
    }
}

// MARK: - Notification Extensions
extension Notification.Name {
    static let openNoteNotification = Notification.Name("openNoteNotification")
    static let showNotesHubNotification = Notification.Name("showNotesHubNotification")
}
