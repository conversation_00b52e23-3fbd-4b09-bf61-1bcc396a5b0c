import Foundation
import SwiftUI
import AppKit

/// Clipboard Manager module for tracking and managing clipboard history
class ClipboardManagerModule: NexusModule {
    let identifier = "clipboard-manager"
    let displayName = "Clipboard Manager"
    var isEnabled = true
    let iconName = "doc.on.clipboard"

    private let clipboardManager = ClipboardManager()
    private var settings: ClipboardModuleSettings = .default
    
    func activate() async throws {
        clipboardManager.configure(with: settings)
        await clipboardManager.startMonitoring()
        print("Clipboard Manager module activated")
    }

    func deactivate() async {
        await clipboardManager.stopMonitoring()
        print("Clipboard Manager module deactivated")
    }

    /// Configure the module with settings
    func configure(with settings: ClipboardModuleSettings) {
        self.settings = settings
        clipboardManager.configure(with: settings)
    }

    func configure(securityManager: SecurityManager) {
        clipboardManager.configure(securityManager: securityManager)
    }
    
    func search(_ query: String) async -> [SearchResult] {
        let history = await clipboardManager.getHistory()
        
        if query.isEmpty {
            // Show recent clipboard items
            return Array(history.prefix(10)).map { item in
                SearchResult(
                    title: item.preview,
                    subtitle: "Copied \(item.timestamp.formatted(.relative(presentation: .named)))",
                    iconName: item.iconName,
                    moduleIdentifier: identifier,
                    action: .copyToClipboard(text: item.content)
                )
            }
        } else {
            // Search clipboard history
            let filteredHistory = history.filter { item in
                item.content.localizedCaseInsensitiveContains(query)
            }
            
            return Array(filteredHistory.prefix(10)).map { item in
                SearchResult(
                    title: item.preview,
                    subtitle: "Copied \(item.timestamp.formatted(.relative(presentation: .named)))",
                    iconName: item.iconName,
                    moduleIdentifier: identifier,
                    action: .copyToClipboard(text: item.content)
                )
            }
        }
    }
}

/// Manages clipboard monitoring and history
@Observable
class ClipboardManager {
    private var history: [ClipboardItem] = []
    private var monitoringTimer: Timer?
    private var lastChangeCount: Int = 0
    private var settings: ClipboardModuleSettings = .default
    private var securityManager: SecurityManager?
    
    init() {
        lastChangeCount = NSPasteboard.general.changeCount
    }

    /// Configure the clipboard manager with settings
    func configure(with settings: ClipboardModuleSettings) {
        self.settings = settings

        // Trim history if needed
        if history.count > settings.maxHistoryItems {
            history = Array(history.prefix(settings.maxHistoryItems))
        }
    }

    /// Configure the clipboard manager with security manager
    func configure(securityManager: SecurityManager) {
        self.securityManager = securityManager
    }
    
    /// Start monitoring clipboard changes
    func startMonitoring() async {
        await MainActor.run {
            monitoringTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { [weak self] _ in
                self?.checkClipboardChanges()
            }
        }
    }
    
    /// Stop monitoring clipboard changes
    func stopMonitoring() async {
        await MainActor.run {
            monitoringTimer?.invalidate()
            monitoringTimer = nil
        }
    }
    
    /// Get clipboard history
    func getHistory() -> [ClipboardItem] {
        return history
    }
    
    /// Check for clipboard changes
    private func checkClipboardChanges() {
        let pasteboard = NSPasteboard.general
        let currentChangeCount = pasteboard.changeCount
        
        if currentChangeCount != lastChangeCount {
            lastChangeCount = currentChangeCount
            
            // Get clipboard content
            if let content = getClipboardContent() {
                addToHistory(content)
            }
        }
    }
    
    /// Get current clipboard content
    private func getClipboardContent() -> ClipboardItem? {
        let pasteboard = NSPasteboard.general
        
        // Check for different types of content
        if let string = pasteboard.string(forType: .string) {
            return ClipboardItem(
                content: encryptContent(string),
                type: .text,
                timestamp: Date()
            )
        } else if let url = pasteboard.string(forType: .URL) {
            return ClipboardItem(
                content: encryptContent(url),
                type: .url,
                timestamp: Date()
            )
        } else if settings.enableFileCapture, let fileURL = pasteboard.string(forType: .fileURL) {
            return ClipboardItem(
                content: encryptContent(fileURL),
                type: .file,
                timestamp: Date()
            )
        } else if settings.enableImageCapture, pasteboard.canReadItem(withDataConformingToTypes: [NSPasteboard.PasteboardType.png.rawValue]) {
            return ClipboardItem(
                content: encryptContent("[Image]"),
                type: .image,
                timestamp: Date()
            )
        }
        
        return nil
    }
    
    /// Add item to history
    private func addToHistory(_ item: ClipboardItem) {
        // Don't add duplicates of the most recent item
        if let lastItem = history.first, lastItem.content == item.content {
            return
        }
        
        // Add to beginning of history
        history.insert(item, at: 0)

        // Limit history size based on settings
        if history.count > settings.maxHistoryItems {
            history = Array(history.prefix(settings.maxHistoryItems))
        }
    }
    
    /// Clear clipboard history
    func clearHistory() {
        history.removeAll()
    }
    
    /// Remove specific item from history
    func removeItem(_ item: ClipboardItem) {
        history.removeAll { $0.id == item.id }
    }

    /// Encrypt clipboard content if security manager is available
    private func encryptContent(_ content: String) -> String {
        guard let securityManager = securityManager else { return content }

        do {
            let encryptedData = try securityManager.encryptClipboardContent(content)
            return encryptedData.base64EncodedString()
        } catch {
            print("Failed to encrypt clipboard content: \(error)")
            return content
        }
    }

    /// Decrypt clipboard content if security manager is available
    private func decryptContent(_ encryptedContent: String) -> String {
        guard let securityManager = securityManager else { return encryptedContent }

        // Try to decode as base64 first
        guard let encryptedData = Data(base64Encoded: encryptedContent) else {
            return encryptedContent // Return as-is if not base64
        }

        do {
            return try securityManager.decryptClipboardContent(encryptedData)
        } catch {
            print("Failed to decrypt clipboard content: \(error)")
            return encryptedContent
        }
    }

    /// Get decrypted content for display
    func getDecryptedContent(for item: ClipboardItem) -> String {
        return decryptContent(item.content)
    }

    /// Get decrypted preview for display
    func getDecryptedPreview(for item: ClipboardItem) -> String {
        let decryptedContent = decryptContent(item.content)

        switch item.type {
        case .text:
            let trimmed = decryptedContent.trimmingCharacters(in: .whitespacesAndNewlines)
            if trimmed.count > 60 {
                return String(trimmed.prefix(60)) + "..."
            }
            return trimmed
        case .url:
            return decryptedContent
        case .file:
            return URL(string: decryptedContent)?.lastPathComponent ?? decryptedContent
        case .image:
            return "[Image]"
        }
    }
}

/// Represents a clipboard item
struct ClipboardItem: Identifiable, Codable {
    let id = UUID()
    let content: String
    let type: ClipboardItemType
    let timestamp: Date
    
    /// Preview text for display
    var preview: String {
        switch type {
        case .text:
            let trimmed = content.trimmingCharacters(in: .whitespacesAndNewlines)
            if trimmed.count > 60 {
                return String(trimmed.prefix(60)) + "..."
            }
            return trimmed
        case .url:
            return content
        case .file:
            return URL(string: content)?.lastPathComponent ?? content
        case .image:
            return "[Image]"
        }
    }
    
    /// Icon name for the item type
    var iconName: String {
        switch type {
        case .text:
            return "doc.text"
        case .url:
            return "link"
        case .file:
            return "doc"
        case .image:
            return "photo"
        }
    }
}

/// Types of clipboard content
enum ClipboardItemType: String, Codable, CaseIterable {
    case text = "text"
    case url = "url"
    case file = "file"
    case image = "image"
    
    var displayName: String {
        switch self {
        case .text:
            return "Text"
        case .url:
            return "URL"
        case .file:
            return "File"
        case .image:
            return "Image"
        }
    }
}

/// SwiftUI view for clipboard history management
struct ClipboardHistoryView: View {
    @State private var clipboardManager = ClipboardManager()
    @State private var searchText = ""
    @State private var selectedType: ClipboardItemType?
    
    var filteredHistory: [ClipboardItem] {
        let history = clipboardManager.getHistory()
        
        var filtered = history
        
        // Filter by type if selected
        if let selectedType = selectedType {
            filtered = filtered.filter { $0.type == selectedType }
        }
        
        // Filter by search text
        if !searchText.isEmpty {
            filtered = filtered.filter { item in
                item.content.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        return filtered
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Header with search and filters
            VStack(spacing: 12) {
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)
                    
                    TextField("Search clipboard history...", text: $searchText)
                        .textFieldStyle(.roundedBorder)
                }
                
                // Type filter
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        Button("All") {
                            selectedType = nil
                        }
                        .buttonStyle(.bordered)
                        .controlSize(.small)
                        .foregroundColor(selectedType == nil ? .primary : .secondary)
                        
                        ForEach(ClipboardItemType.allCases, id: \.self) { type in
                            Button(type.displayName) {
                                selectedType = selectedType == type ? nil : type
                            }
                            .buttonStyle(.bordered)
                            .controlSize(.small)
                            .foregroundColor(selectedType == type ? .primary : .secondary)
                        }
                    }
                    .padding(.horizontal)
                }
            }
            .padding()
            .background(Color(NSColor.controlBackgroundColor))
            
            Divider()
            
            // History list
            if filteredHistory.isEmpty {
                VStack(spacing: 16) {
                    Image(systemName: "doc.on.clipboard")
                        .font(.system(size: 48))
                        .foregroundColor(.secondary)
                    
                    Text("No clipboard history")
                        .font(.title2)
                        .foregroundColor(.secondary)
                    
                    Text("Copy something to see it here")
                        .font(.body)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                List(filteredHistory, id: \.id) { item in
                    ClipboardItemRow(item: item) {
                        // Copy to clipboard
                        let pasteboard = NSPasteboard.general
                        pasteboard.clearContents()
                        pasteboard.setString(item.content, forType: .string)
                    }
                }
                .listStyle(.plain)
            }
        }
        .onAppear {
            Task {
                await clipboardManager.startMonitoring()
            }
        }
        .onDisappear {
            Task {
                await clipboardManager.stopMonitoring()
            }
        }
    }
}

/// Row view for clipboard items
struct ClipboardItemRow: View {
    let item: ClipboardItem
    let onCopy: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: item.iconName)
                .foregroundColor(.accentColor)
                .frame(width: 20, height: 20)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(item.preview)
                    .font(.body)
                    .lineLimit(2)
                
                HStack {
                    Text(item.type.displayName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.secondary.opacity(0.2))
                        .cornerRadius(4)
                    
                    Text(item.timestamp.formatted(.relative(presentation: .named)))
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                }
            }
            
            Button(action: onCopy) {
                Image(systemName: "doc.on.doc")
                    .foregroundColor(.secondary)
            }
            .buttonStyle(.plain)
            .help("Copy to clipboard")
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .onTapGesture {
            onCopy()
        }
    }
}
