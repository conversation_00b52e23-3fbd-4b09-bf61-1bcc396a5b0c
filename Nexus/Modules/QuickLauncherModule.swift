import Foundation
import SwiftUI
import AppKit

/// Quick Launcher module for finding and launching applications
class QuickLauncherModule: NexusModule {
    let identifier = "quick-launcher"
    let displayName = "Quick Launcher"
    var isEnabled = true
    let iconName = "app.badge"
    
    private let appFinder = ApplicationFinder()
    
    func activate() async throws {
        await appFinder.indexApplications()
        print("Quick Launcher module activated")
    }
    
    func deactivate() async {
        print("Quick Launcher module deactivated")
    }
    
    func search(_ query: String) async -> [SearchResult] {
        guard !query.isEmpty else { return [] }
        
        let apps = await appFinder.searchApplications(query: query)
        
        return apps.map { app in
            SearchResult(
                title: app.name,
                subtitle: app.path,
                iconName: "app",
                moduleIdentifier: identifier,
                action: .launchApplication(path: app.path)
            )
        }
    }
}

/// Finds and indexes applications on the system
@Observable
class ApplicationFinder {
    private var applications: [Application] = []
    private var recentApplications: [Application] = []
    private let maxRecentApps = 10
    
    /// Index all applications on the system
    func indexApplications() async {
        await withTaskGroup(of: [Application].self) { group in
            // Search common application directories
            let searchPaths = [
                "/Applications",
                "/System/Applications",
                "/System/Library/CoreServices",
                "~/Applications".expandingTildeInPath
            ]
            
            for path in searchPaths {
                group.addTask {
                    await self.findApplicationsInDirectory(path)
                }
            }
            
            var allApps: [Application] = []
            for await apps in group {
                allApps.append(contentsOf: apps)
            }
            
            await MainActor.run {
                // Remove duplicates and sort
                let uniqueApps = Dictionary(grouping: allApps, by: \.name)
                    .compactMapValues { $0.first }
                    .values
                
                self.applications = Array(uniqueApps).sorted { $0.name < $1.name }
            }
        }
    }
    
    /// Search applications by query
    func searchApplications(query: String) -> [Application] {
        let lowercaseQuery = query.lowercased()
        
        // First, check recent applications
        let recentMatches = recentApplications.filter { app in
            app.name.lowercased().contains(lowercaseQuery)
        }
        
        // Then search all applications
        let allMatches = applications.filter { app in
            app.name.lowercased().contains(lowercaseQuery) ||
            app.bundleIdentifier?.lowercased().contains(lowercaseQuery) == true
        }
        
        // Combine and deduplicate, prioritizing recent apps
        var results: [Application] = []
        var seenNames = Set<String>()
        
        // Add recent matches first
        for app in recentMatches {
            if !seenNames.contains(app.name) {
                results.append(app)
                seenNames.insert(app.name)
            }
        }
        
        // Add other matches
        for app in allMatches {
            if !seenNames.contains(app.name) {
                results.append(app)
                seenNames.insert(app.name)
            }
        }
        
        // Sort by relevance (exact matches first, then starts with, then contains)
        return results.sorted { app1, app2 in
            let name1 = app1.name.lowercased()
            let name2 = app2.name.lowercased()
            
            // Exact matches first
            if name1 == lowercaseQuery && name2 != lowercaseQuery { return true }
            if name2 == lowercaseQuery && name1 != lowercaseQuery { return false }
            
            // Starts with query
            if name1.hasPrefix(lowercaseQuery) && !name2.hasPrefix(lowercaseQuery) { return true }
            if name2.hasPrefix(lowercaseQuery) && !name1.hasPrefix(lowercaseQuery) { return false }
            
            // Alphabetical fallback
            return name1 < name2
        }
    }
    
    /// Record that an application was launched
    func recordLaunch(_ application: Application) {
        // Remove if already in recent list
        recentApplications.removeAll { $0.name == application.name }
        
        // Add to front of recent list
        recentApplications.insert(application, at: 0)
        
        // Limit size
        if recentApplications.count > maxRecentApps {
            recentApplications = Array(recentApplications.prefix(maxRecentApps))
        }
    }
    
    /// Get recent applications
    func getRecentApplications() -> [Application] {
        return recentApplications
    }
    
    /// Find applications in a specific directory
    private func findApplicationsInDirectory(_ path: String) async -> [Application] {
        let fileManager = FileManager.default
        var applications: [Application] = []
        
        guard let enumerator = fileManager.enumerator(atPath: path) else {
            return applications
        }
        
        for case let fileName as String in enumerator {
            if fileName.hasSuffix(".app") {
                let fullPath = "\(path)/\(fileName)"
                
                if let app = createApplication(from: fullPath) {
                    applications.append(app)
                }
                
                // Don't recurse into .app bundles
                if fileName.hasSuffix(".app") {
                    enumerator.skipDescendants()
                }
            }
        }
        
        return applications
    }
    
    /// Create an Application object from a path
    private func createApplication(from path: String) -> Application? {
        let url = URL(fileURLWithPath: path)
        
        // Get app name from bundle or filename
        var name = url.deletingPathExtension().lastPathComponent
        var bundleIdentifier: String?
        
        // Try to get info from Info.plist
        let infoPlistPath = url.appendingPathComponent("Contents/Info.plist").path
        if let plistData = FileManager.default.contents(atPath: infoPlistPath),
           let plist = try? PropertyListSerialization.propertyList(from: plistData, format: nil) as? [String: Any] {
            
            if let displayName = plist["CFBundleDisplayName"] as? String {
                name = displayName
            } else if let bundleName = plist["CFBundleName"] as? String {
                name = bundleName
            }
            
            bundleIdentifier = plist["CFBundleIdentifier"] as? String
        }
        
        return Application(
            name: name,
            path: path,
            bundleIdentifier: bundleIdentifier
        )
    }
}

/// Represents an application
struct Application: Identifiable, Codable {
    let id = UUID()
    let name: String
    let path: String
    let bundleIdentifier: String?
    
    /// Launch this application
    func launch() {
        let url = URL(fileURLWithPath: path)
        NSWorkspace.shared.openApplication(at: url, configuration: NSWorkspace.OpenConfiguration()) { _, error in
            if let error = error {
                print("Failed to launch \(name): \(error)")
            }
        }
    }
}

/// SwiftUI view for application launcher
struct QuickLauncherView: View {
    @State private var appFinder = ApplicationFinder()
    @State private var searchText = ""
    @State private var isIndexing = false
    
    var searchResults: [Application] {
        if searchText.isEmpty {
            return appFinder.getRecentApplications()
        } else {
            return Array(appFinder.searchApplications(query: searchText).prefix(20))
        }
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            VStack(spacing: 12) {
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)
                    
                    TextField("Search applications...", text: $searchText)
                        .textFieldStyle(.roundedBorder)
                }
                
                if isIndexing {
                    HStack {
                        ProgressView()
                            .scaleEffect(0.8)
                        Text("Indexing applications...")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
            .background(Color(NSColor.controlBackgroundColor))
            
            Divider()
            
            // Results
            if searchResults.isEmpty && !isIndexing {
                VStack(spacing: 16) {
                    Image(systemName: "app.badge")
                        .font(.system(size: 48))
                        .foregroundColor(.secondary)
                    
                    Text(searchText.isEmpty ? "No recent applications" : "No applications found")
                        .font(.title2)
                        .foregroundColor(.secondary)
                    
                    if searchText.isEmpty {
                        Text("Launch an app to see it here")
                            .font(.body)
                            .foregroundColor(.secondary)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                List(searchResults, id: \.id) { app in
                    ApplicationRow(application: app) {
                        app.launch()
                        appFinder.recordLaunch(app)
                    }
                }
                .listStyle(.plain)
            }
        }
        .onAppear {
            indexApplications()
        }
    }
    
    private func indexApplications() {
        isIndexing = true
        Task {
            await appFinder.indexApplications()
            await MainActor.run {
                isIndexing = false
            }
        }
    }
}

/// Row view for applications
struct ApplicationRow: View {
    let application: Application
    let onLaunch: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // App icon (placeholder for now)
            Image(systemName: "app")
                .foregroundColor(.accentColor)
                .frame(width: 32, height: 32)
                .background(Color.secondary.opacity(0.1))
                .cornerRadius(8)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(application.name)
                    .font(.body)
                    .lineLimit(1)
                
                Text(application.path)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            
            Spacer()
            
            Button(action: onLaunch) {
                Image(systemName: "arrow.up.right.square")
                    .foregroundColor(.secondary)
            }
            .buttonStyle(.plain)
            .help("Launch application")
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .onTapGesture {
            onLaunch()
        }
    }
}

// MARK: - String Extension
extension String {
    var expandingTildeInPath: String {
        return NSString(string: self).expandingTildeInPath
    }
}
