import SwiftUI
import AppKit

/// Dedicated window for notes management
class NotesWindowController: NSWindowController {
    private var dataService: DataService
    
    init(dataService: DataService) {
        self.dataService = dataService
        
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 800, height: 600),
            styleMask: [.titled, .closable, .miniaturizable, .resizable],
            backing: .buffered,
            defer: false
        )
        
        super.init(window: window)
        
        window.title = "Nexus Notes"
        window.center()
        window.setFrameAutosaveName("NotesWindow")
        
        let contentView = NotesWindowView()
            .environment(\.dataService, dataService)
        
        window.contentViewController = NSHostingController(rootView: contentView)
        
        // Listen for note opening notifications
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(openNote(_:)),
            name: .openNoteNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(showNotesHub),
            name: .showNotesHubNotification,
            object: nil
        )
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc private func openNote(_ notification: Notification) {
        guard let note = notification.object as? Note else { return }
        
        // Show the window and focus on the specific note
        showWindow(nil)
        window?.makeKeyAndOrderFront(nil)
        
        // Post notification to select the note in the UI
        NotificationCenter.default.post(
            name: .selectNoteNotification,
            object: note
        )
    }
    
    @objc private func showNotesHub() {
        showWindow(nil)
        window?.makeKeyAndOrderFront(nil)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

/// Main notes window view
struct NotesWindowView: View {
    @Environment(\.dataService) private var dataService
    @State private var selectedNote: Note?
    @State private var searchText = ""
    @State private var showingNewNote = false
    @State private var selectedFolder: Folder?
    
    var filteredNotes: [Note] {
        if searchText.isEmpty {
            return dataService.notes.filter { !$0.isArchived }
        } else {
            return dataService.notes.filter { note in
                !note.isArchived && (
                    note.title.localizedCaseInsensitiveContains(searchText) ||
                    note.content.localizedCaseInsensitiveContains(searchText)
                )
            }
        }
    }
    
    var body: some View {
        NavigationSplitView {
            // Sidebar with notes list
            VStack(spacing: 0) {
                // Search bar
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)
                    
                    TextField("Search notes...", text: $searchText)
                        .textFieldStyle(.roundedBorder)
                }
                .padding()
                
                // Notes list
                List(filteredNotes, id: \.id, selection: $selectedNote) { note in
                    NotesListRow(note: note)
                        .tag(note)
                }
                .listStyle(.sidebar)
            }
            .navigationSplitViewColumnWidth(min: 250, ideal: 300, max: 400)
            .toolbar {
                ToolbarItemGroup(placement: .primaryAction) {
                    Button(action: { showingNewNote = true }) {
                        Image(systemName: "plus")
                    }
                    .help("New Note")
                    
                    Button(action: refreshNotes) {
                        Image(systemName: "arrow.clockwise")
                    }
                    .help("Refresh")
                }
            }
        } detail: {
            // Detail view for selected note
            if let note = selectedNote {
                NoteEditorView(note: note)
            } else {
                VStack(spacing: 16) {
                    Image(systemName: "note.text")
                        .font(.system(size: 48))
                        .foregroundColor(.secondary)
                    
                    Text("Select a note to view")
                        .font(.title2)
                        .foregroundColor(.secondary)
                    
                    Text("Choose a note from the sidebar or create a new one")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .sheet(isPresented: $showingNewNote) {
            NewNoteSheet()
        }
        .onReceive(NotificationCenter.default.publisher(for: .selectNoteNotification)) { notification in
            if let note = notification.object as? Note {
                selectedNote = note
            }
        }
        .onAppear {
            // Select first note if none selected
            if selectedNote == nil && !filteredNotes.isEmpty {
                selectedNote = filteredNotes.first
            }
        }
    }
    
    private func refreshNotes() {
        dataService.loadData()
    }
}

/// Row view for notes list
struct NotesListRow: View {
    let note: Note
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(note.title)
                .font(.headline)
                .lineLimit(1)
            
            Text(note.content)
                .font(.caption)
                .foregroundColor(.secondary)
                .lineLimit(2)
            
            HStack {
                Text(note.modifiedAt, style: .relative)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                if note.isFavorite {
                    Image(systemName: "heart.fill")
                        .foregroundColor(.red)
                        .font(.caption)
                }
            }
        }
        .padding(.vertical, 2)
    }
}

/// Enhanced note editor view
struct NoteEditorView: View {
    @Environment(\.dataService) private var dataService
    @State private var note: Note
    @State private var editedContent: String
    @State private var isEditing = false
    
    init(note: Note) {
        self._note = State(initialValue: note)
        self._editedContent = State(initialValue: note.content)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header with note metadata and actions
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(note.title)
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    HStack {
                        Text("Modified \(note.modifiedAt, style: .relative)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("•")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("\(editedContent.count) characters")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                HStack(spacing: 8) {
                    Button(action: { toggleFavorite() }) {
                        Image(systemName: note.isFavorite ? "heart.fill" : "heart")
                            .foregroundColor(note.isFavorite ? .red : .secondary)
                    }
                    .buttonStyle(.plain)
                    .help(note.isFavorite ? "Remove from favorites" : "Add to favorites")
                    
                    Button(action: { toggleArchive() }) {
                        Image(systemName: note.isArchived ? "archivebox.fill" : "archivebox")
                            .foregroundColor(note.isArchived ? .orange : .secondary)
                    }
                    .buttonStyle(.plain)
                    .help(note.isArchived ? "Unarchive" : "Archive")
                    
                    Button(action: { exportNote() }) {
                        Image(systemName: "square.and.arrow.up")
                            .foregroundColor(.secondary)
                    }
                    .buttonStyle(.plain)
                    .help("Export note")
                }
            }
            .padding()
            .background(Color(NSColor.controlBackgroundColor))
            
            Divider()
            
            // Note content editor
            ScrollView {
                TextEditor(text: $editedContent)
                    .font(.body)
                    .scrollContentBackground(.hidden)
                    .onChange(of: editedContent) { _, newValue in
                        saveChanges(newValue)
                    }
            }
            .background(Color(NSColor.textBackgroundColor))
        }
        .onChange(of: note) { _, newNote in
            editedContent = newNote.content
        }
    }
    
    private func saveChanges(_ newContent: String) {
        var updatedNote = note
        updatedNote.content = newContent
        updatedNote.modifiedAt = Date()
        dataService.updateNote(updatedNote)
        note = updatedNote
    }
    
    private func toggleFavorite() {
        dataService.toggleFavorite(note)
        note.isFavorite.toggle()
    }
    
    private func toggleArchive() {
        dataService.toggleArchive(note)
        note.isArchived.toggle()
    }
    
    private func exportNote() {
        if let url = dataService.exportNote(note) {
            NSWorkspace.shared.open(url)
        }
    }
}

/// Sheet for creating new notes
struct NewNoteSheet: View {
    @Environment(\.dataService) private var dataService
    @Environment(\.dismiss) private var dismiss
    @State private var content = ""
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("New Note")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("Cancel") {
                    dismiss()
                }
                .buttonStyle(.plain)
            }
            
            TextEditor(text: $content)
                .font(.body)
                .border(Color.secondary.opacity(0.3))
            
            HStack {
                Spacer()
                
                Button("Save") {
                    _ = dataService.createNote(content: content)
                    dismiss()
                }
                .buttonStyle(.borderedProminent)
                .disabled(content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            }
        }
        .padding()
        .frame(width: 500, height: 400)
    }
}

// MARK: - Additional Notification Extensions
extension Notification.Name {
    static let selectNoteNotification = Notification.Name("selectNoteNotification")
}
