import SwiftUI
import AppKit

/// Window controller for the settings window
class SettingsWindowController: NSWindowController {
    private var configurationManager: ConfigurationManager
    private var moduleManager: ModuleManager
    
    init(configurationManager: ConfigurationManager, moduleManager: ModuleManager) {
        self.configurationManager = configurationManager
        self.moduleManager = moduleManager
        
        // Create the window
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 800, height: 600),
            styleMask: [.titled, .closable, .miniaturizable, .resizable],
            backing: .buffered,
            defer: false
        )
        
        super.init(window: window)
        
        setupWindow()
        setupContent()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupWindow() {
        guard let window = window else { return }
        
        window.title = "Nexus Settings"
        window.center()
        window.setFrameAutosaveName("SettingsWindow")
        window.isReleasedWhenClosed = false
        
        // Set minimum size
        window.minSize = NSSize(width: 700, height: 500)
        
        // Configure window appearance
        window.titlebarAppearsTransparent = false
        window.titleVisibility = .visible
    }
    
    private func setupContent() {
        guard let window = window else { return }
        
        // Create the SwiftUI view with environment objects
        let settingsView = SettingsView()
            .environment(\.configurationManager, configurationManager)
            .environment(\.moduleManager, moduleManager)
        
        // Create hosting view
        let hostingView = NSHostingView(rootView: settingsView)
        hostingView.translatesAutoresizingMaskIntoConstraints = false
        
        // Set as window content
        window.contentView = hostingView
    }
    
    /// Show the settings window
    func showSettings() {
        guard let window = window else { return }
        
        if !window.isVisible {
            window.center()
        }
        
        window.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)
    }
    
    /// Hide the settings window
    func hideSettings() {
        window?.orderOut(nil)
    }
    
    /// Toggle the settings window visibility
    func toggleSettings() {
        guard let window = window else { return }
        
        if window.isVisible {
            hideSettings()
        } else {
            showSettings()
        }
    }
}


