import SwiftUI
import KeyboardShortcuts

/// Main settings view for Nexus application
struct SettingsView: View {
    @Environment(\.configurationManager) private var configurationManager
    @Environment(\.moduleManager) private var moduleManager
    @State private var selectedTab: SettingsTab = .modules
    
    enum SettingsTab: String, CaseIterable {
        case modules = "Modules"
        case editor = "Editor"
        case hotkeys = "Hotkeys"
        
        var iconName: String {
            switch self {
            case .modules: return "puzzlepiece.extension"
            case .editor: return "doc.text"
            case .hotkeys: return "keyboard"
            }
        }
    }
    
    var body: some View {
        NavigationSplitView {
            // Sidebar
            List(SettingsTab.allCases, id: \.self, selection: $selectedTab) { tab in
                Label(tab.rawValue, systemImage: tab.iconName)
                    .tag(tab)
            }
            .navigationTitle("Settings")
            .frame(minWidth: 200)
        } detail: {
            // Detail view
            Group {
                switch selectedTab {
                case .modules:
                    ModulesSettingsView()
                case .editor:
                    EditorSettingsView()
                case .hotkeys:
                    HotkeysSettingsView()
                }
            }
            .frame(minWidth: 500, minHeight: 400)
        }
    }
}

/// Settings view for managing modules
struct ModulesSettingsView: View {
    @Environment(\.configurationManager) private var configurationManager
    @Environment(\.moduleManager) private var moduleManager
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                Text("Module Management")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("Enable or disable modules and configure their settings.")
                    .foregroundColor(.secondary)
                
                LazyVStack(spacing: 16) {
                    ModuleSettingsCard(
                        moduleId: "smart-calculator",
                        title: "Smart Calculator",
                        description: "Mathematical expressions and unit conversions",
                        iconName: "function"
                    ) {
                        CalculatorSettingsView()
                    }
                    
                    ModuleSettingsCard(
                        moduleId: "clipboard-manager",
                        title: "Clipboard Manager",
                        description: "Track and manage clipboard history",
                        iconName: "doc.on.clipboard"
                    ) {
                        ClipboardSettingsView()
                    }
                    
                    ModuleSettingsCard(
                        moduleId: "notes-hub",
                        title: "Notes Hub",
                        description: "Create and manage notes and folders",
                        iconName: "note.text"
                    ) {
                        NotesHubSettingsView()
                    }
                    
                    ModuleSettingsCard(
                        moduleId: "quick-launcher",
                        title: "Quick Launcher",
                        description: "Launch applications quickly",
                        iconName: "rocket"
                    ) {
                        QuickLauncherSettingsView()
                    }
                }
            }
            .padding()
        }
        .navigationTitle("Modules")
    }
}

/// Reusable card component for module settings
struct ModuleSettingsCard<Content: View>: View {
    let moduleId: String
    let title: String
    let description: String
    let iconName: String
    @ViewBuilder let content: Content
    
    @Environment(\.configurationManager) private var configurationManager
    @Environment(\.moduleManager) private var moduleManager
    @State private var isExpanded = false
    
    var isEnabled: Bool {
        moduleManager.isModuleEnabled(moduleId)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header
            HStack {
                Image(systemName: iconName)
                    .foregroundColor(isEnabled ? .blue : .secondary)
                    .frame(width: 24, height: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.headline)
                        .foregroundColor(isEnabled ? .primary : .secondary)
                    
                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Toggle("", isOn: Binding(
                    get: { isEnabled },
                    set: { _ in
                        moduleManager.toggleModule(moduleId)
                        Task {
                            await moduleManager.refreshModules()
                        }
                    }
                ))
                .toggleStyle(SwitchToggleStyle())
                
                Button(action: { isExpanded.toggle() }) {
                    Image(systemName: "chevron.right")
                        .rotationEffect(.degrees(isExpanded ? 90 : 0))
                        .animation(.easeInOut(duration: 0.2), value: isExpanded)
                }
                .buttonStyle(PlainButtonStyle())
                .disabled(!isEnabled)
            }
            .padding()
            .background(Color(NSColor.controlBackgroundColor))
            
            // Expandable content
            if isExpanded && isEnabled {
                Divider()
                content
                    .padding()
                    .background(Color(NSColor.controlBackgroundColor).opacity(0.5))
            }
        }
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color(NSColor.separatorColor), lineWidth: 1)
        )
    }
}

/// Settings view for the Calculator module
struct CalculatorSettingsView: View {
    @Environment(\.configurationManager) private var configurationManager
    @State private var settings: CalculatorModuleSettings
    
    init() {
        // This will be properly initialized in onAppear
        _settings = State(initialValue: CalculatorModuleSettings.default)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Decimal Places:")
                Spacer()
                Stepper(value: $settings.decimalPlaces, in: 0...10) {
                    Text("\(settings.decimalPlaces)")
                        .frame(width: 30)
                }
            }
            
            HStack {
                Text("Angle Unit:")
                Spacer()
                Picker("", selection: $settings.angleUnit) {
                    ForEach(CalculatorModuleSettings.AngleUnit.allCases, id: \.self) { unit in
                        Text(unit.displayName).tag(unit)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .frame(width: 150)
            }
            
            Toggle("Enable Unit Conversions", isOn: $settings.enableUnitConversions)
            Toggle("Enable Currency Conversions", isOn: $settings.enableCurrencyConversions)
        }
        .onAppear {
            settings = configurationManager.configuration.calculatorSettings
        }
        .onChange(of: settings) { _, newSettings in
            configurationManager.updateCalculatorSettings(newSettings)
        }
    }
}

/// Settings view for the Clipboard module
struct ClipboardSettingsView: View {
    @Environment(\.configurationManager) private var configurationManager
    @State private var settings: ClipboardModuleSettings
    
    init() {
        _settings = State(initialValue: ClipboardModuleSettings.default)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Max History Items:")
                Spacer()
                Stepper(value: $settings.maxHistoryItems, in: 10...500, step: 10) {
                    Text("\(settings.maxHistoryItems)")
                        .frame(width: 50)
                }
            }
            
            HStack {
                Text("Auto Cleanup (days):")
                Spacer()
                Stepper(value: $settings.autoCleanupDays, in: 1...365) {
                    Text("\(settings.autoCleanupDays)")
                        .frame(width: 50)
                }
            }
            
            Toggle("Capture Images", isOn: $settings.enableImageCapture)
            Toggle("Capture Files", isOn: $settings.enableFileCapture)
        }
        .onAppear {
            settings = configurationManager.configuration.clipboardSettings
        }
        .onChange(of: settings) { _, newSettings in
            configurationManager.updateClipboardSettings(newSettings)
        }
    }
}

/// Settings view for the Notes Hub module
struct NotesHubSettingsView: View {
    @Environment(\.configurationManager) private var configurationManager
    @State private var settings: NotesHubModuleSettings
    
    init() {
        _settings = State(initialValue: NotesHubModuleSettings.default)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Default Format:")
                Spacer()
                Picker("", selection: $settings.defaultNoteFormat) {
                    ForEach(NotesHubModuleSettings.NoteFormat.allCases, id: \.self) { format in
                        Text(format.displayName).tag(format)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .frame(width: 150)
            }
            
            HStack {
                Text("Auto Save Interval:")
                Spacer()
                Stepper(value: $settings.autoSaveInterval, in: 5...300, step: 5) {
                    Text("\(settings.autoSaveInterval)s")
                        .frame(width: 50)
                }
            }
            
            Toggle("Enable Auto Save", isOn: $settings.enableAutoSave)
            Toggle("Show Preview in Search", isOn: $settings.showPreviewInSearch)
        }
        .onAppear {
            settings = configurationManager.configuration.notesHubSettings
        }
        .onChange(of: settings) { _, newSettings in
            configurationManager.updateNotesHubSettings(newSettings)
        }
    }
}

/// Settings view for the Quick Launcher module
struct QuickLauncherSettingsView: View {
    @Environment(\.configurationManager) private var configurationManager
    @State private var settings: QuickLauncherModuleSettings
    
    init() {
        _settings = State(initialValue: QuickLauncherModuleSettings.default)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Max Recent Items:")
                Spacer()
                Stepper(value: $settings.maxRecentItems, in: 5...100, step: 5) {
                    Text("\(settings.maxRecentItems)")
                        .frame(width: 50)
                }
            }
            
            Toggle("Index System Apps", isOn: $settings.indexSystemApps)
            Toggle("Index User Apps", isOn: $settings.indexUserApps)
            Toggle("Enable Fuzzy Search", isOn: $settings.enableFuzzySearch)
        }
        .onAppear {
            settings = configurationManager.configuration.quickLauncherSettings
        }
        .onChange(of: settings) { _, newSettings in
            configurationManager.updateQuickLauncherSettings(newSettings)
        }
    }
}

/// Settings view for editor configuration
struct EditorSettingsView: View {
    @Environment(\.configurationManager) private var configurationManager
    @State private var settings: EditorSettings

    init() {
        _settings = State(initialValue: EditorSettings.default)
    }

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                Text("Editor Settings")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text("Configure the text editor appearance and behavior.")
                    .foregroundColor(.secondary)

                VStack(alignment: .leading, spacing: 16) {
                    GroupBox("Appearance") {
                        VStack(alignment: .leading, spacing: 12) {
                            HStack {
                                Text("Font Size:")
                                Spacer()
                                Stepper(value: $settings.fontSize, in: 8...24, step: 1) {
                                    Text("\(Int(settings.fontSize))pt")
                                        .frame(width: 40)
                                }
                            }

                            HStack {
                                Text("Font Family:")
                                Spacer()
                                Picker("", selection: $settings.fontFamily) {
                                    Text("SF Mono").tag("SF Mono")
                                    Text("Menlo").tag("Menlo")
                                    Text("Monaco").tag("Monaco")
                                    Text("Courier New").tag("Courier New")
                                }
                                .pickerStyle(MenuPickerStyle())
                                .frame(width: 120)
                            }

                            HStack {
                                Text("Line Spacing:")
                                Spacer()
                                Stepper(value: $settings.lineSpacing, in: 1.0...2.0, step: 0.1) {
                                    Text(String(format: "%.1f", settings.lineSpacing))
                                        .frame(width: 30)
                                }
                            }
                        }
                        .padding()
                    }

                    GroupBox("Features") {
                        VStack(alignment: .leading, spacing: 12) {
                            Toggle("Show Line Numbers", isOn: $settings.showLineNumbers)
                            Toggle("Wrap Text", isOn: $settings.wrapText)
                            Toggle("Markdown Rendering", isOn: $settings.markdownRenderingEnabled)
                            Toggle("Syntax Highlighting", isOn: $settings.syntaxHighlightingEnabled)
                        }
                        .padding()
                    }
                }
            }
            .padding()
        }
        .navigationTitle("Editor")
        .onAppear {
            settings = configurationManager.configuration.editorSettings
        }
        .onChange(of: settings) { _, newSettings in
            configurationManager.updateEditorSettings(newSettings)
        }
    }
}

/// Settings view for keyboard shortcuts
struct HotkeysSettingsView: View {
    @Environment(\.configurationManager) private var configurationManager

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                Text("Keyboard Shortcuts")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text("Configure global and module-specific keyboard shortcuts.")
                    .foregroundColor(.secondary)

                VStack(alignment: .leading, spacing: 16) {
                    GroupBox("Global Shortcuts") {
                        VStack(alignment: .leading, spacing: 12) {
                            HStack {
                                Text("Toggle Nexus:")
                                Spacer()
                                KeyboardShortcuts.Recorder("Toggle Nexus", name: .toggleNexus)
                                    .frame(width: 150)
                            }
                        }
                        .padding()
                    }

                    GroupBox("Module Shortcuts") {
                        VStack(alignment: .leading, spacing: 12) {
                            HStack {
                                Text("Smart Calculator:")
                                Spacer()
                                KeyboardShortcuts.Recorder("Calculator", name: .calculatorModule)
                                    .frame(width: 150)
                            }

                            HStack {
                                Text("Clipboard Manager:")
                                Spacer()
                                KeyboardShortcuts.Recorder("Clipboard", name: .clipboardModule)
                                    .frame(width: 150)
                            }

                            HStack {
                                Text("Notes Hub:")
                                Spacer()
                                KeyboardShortcuts.Recorder("Notes", name: .notesModule)
                                    .frame(width: 150)
                            }

                            HStack {
                                Text("Quick Launcher:")
                                Spacer()
                                KeyboardShortcuts.Recorder("Launcher", name: .launcherModule)
                                    .frame(width: 150)
                            }
                        }
                        .padding()
                    }
                }
            }
            .padding()
        }
        .navigationTitle("Hotkeys")
    }
}


