import Foundation
import KeyboardShortcuts

/// Core data models for Nexus

/// Represents a note in the system
struct Note: Identifiable, Codable, Hashable {
    let id: UUID
    var content: String
    var title: String
    let createdAt: Date
    var modifiedAt: Date
    var isFavorite: Bool
    var isArchived: Bool

    init(
        id: UUID = UUID(),
        content: String = "",
        title: String = "",
        createdAt: Date = Date(),
        modifiedAt: Date = Date(),
        isFavorite: Bool = false,
        isArchived: Bool = false
    ) {
        self.id = id
        self.content = content
        self.title = title.isEmpty ? Note.generateTitle(from: content) : title
        self.createdAt = createdAt
        self.modifiedAt = modifiedAt
        self.isFavorite = isFavorite
        self.isArchived = isArchived
    }

    /// Generate a title from content
    static func generateTitle(from content: String) -> String {
        let lines = content.components(separatedBy: .newlines)
        let firstLine = lines.first?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""

        if firstLine.isEmpty {
            return "Untitled Note"
        }

        // Remove markdown heading syntax
        let title = firstLine.replacingOccurrences(of: "^#+\\s*", with: "", options: .regularExpression)

        // Limit length
        if title.count > 50 {
            return String(title.prefix(47)) + "..."
        }

        return title
    }

    /// Update the note's content and refresh title and modified date
    mutating func updateContent(_ newContent: String) {
        content = newContent
        title = Note.generateTitle(from: newContent)
        modifiedAt = Date()
    }
}

/// Represents a folder for organizing notes
struct Folder: Identifiable, Codable {
    let id: UUID
    var name: String
    var color: String?
    let createdAt: Date
    var parentId: UUID?

    init(
        id: UUID = UUID(),
        name: String,
        color: String? = nil,
        createdAt: Date = Date(),
        parentId: UUID? = nil
    ) {
        self.id = id
        self.name = name
        self.color = color
        self.createdAt = createdAt
        self.parentId = parentId
    }
}

/// Main application configuration
struct AppConfiguration: Codable {
    let hotkeys: [String: KeyboardShortcutData]
    let editorSettings: EditorSettings
    let moduleSettings: [String: ModuleSettings]
    let securitySettings: SecuritySettings

    // Typed module settings accessors
    var calculatorSettings: CalculatorModuleSettings {
        get {
            if let settings = moduleSettings["smart-calculator"],
               let data = try? JSONEncoder().encode(settings),
               let typed = try? JSONDecoder().decode(CalculatorModuleSettings.self, from: data) {
                return typed
            }
            return CalculatorModuleSettings.default
        }
    }

    var clipboardSettings: ClipboardModuleSettings {
        get {
            if let settings = moduleSettings["clipboard-manager"],
               let data = try? JSONEncoder().encode(settings),
               let typed = try? JSONDecoder().decode(ClipboardModuleSettings.self, from: data) {
                return typed
            }
            return ClipboardModuleSettings.default
        }
    }

    var notesHubSettings: NotesHubModuleSettings {
        get {
            if let settings = moduleSettings["notes-hub"],
               let data = try? JSONEncoder().encode(settings),
               let typed = try? JSONDecoder().decode(NotesHubModuleSettings.self, from: data) {
                return typed
            }
            return NotesHubModuleSettings.default
        }
    }

    var quickLauncherSettings: QuickLauncherModuleSettings {
        get {
            if let settings = moduleSettings["quick-launcher"],
               let data = try? JSONEncoder().encode(settings),
               let typed = try? JSONDecoder().decode(QuickLauncherModuleSettings.self, from: data) {
                return typed
            }
            return QuickLauncherModuleSettings.default
        }
    }

    static let `default` = AppConfiguration(
        hotkeys: [
            "toggleNexus": KeyboardShortcutData(key: "space", modifiers: ["command", "shift"])
        ],
        editorSettings: EditorSettings.default,
        moduleSettings: createDefaultModuleSettings(),
        securitySettings: SecuritySettings.default
    )

    private static func createDefaultModuleSettings() -> [String: ModuleSettings] {
        var settings: [String: ModuleSettings] = [:]

        // Calculator settings
        if let data = try? JSONEncoder().encode(CalculatorModuleSettings.default),
           let dict = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
            settings["smart-calculator"] = ModuleSettings(dict)
        }

        // Clipboard settings
        if let data = try? JSONEncoder().encode(ClipboardModuleSettings.default),
           let dict = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
            settings["clipboard-manager"] = ModuleSettings(dict)
        }

        // Notes Hub settings
        if let data = try? JSONEncoder().encode(NotesHubModuleSettings.default),
           let dict = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
            settings["notes-hub"] = ModuleSettings(dict)
        }

        // Quick Launcher settings
        if let data = try? JSONEncoder().encode(QuickLauncherModuleSettings.default),
           let dict = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
            settings["quick-launcher"] = ModuleSettings(dict)
        }

        return settings
    }
}

/// Keyboard shortcut data for persistence
struct KeyboardShortcutData: Codable, Equatable {
    let key: String
    let modifiers: [String]
}

/// Editor-specific settings
struct EditorSettings: Codable, Equatable {
    var fontSize: Double
    var fontFamily: String
    var lineSpacing: Double
    var showLineNumbers: Bool
    var wrapText: Bool
    var markdownRenderingEnabled: Bool
    var syntaxHighlightingEnabled: Bool
    
    static let `default` = EditorSettings(
        fontSize: 14.0,
        fontFamily: "SF Mono",
        lineSpacing: 1.2,
        showLineNumbers: false,
        wrapText: true,
        markdownRenderingEnabled: false,
        syntaxHighlightingEnabled: true
    )
}

/// Generic module settings container
struct ModuleSettings: Codable {
    let settings: [String: AnyCodable]

    init(_ settings: [String: Any] = [:]) {
        self.settings = settings.mapValues { AnyCodable($0) }
    }

    subscript<T: Codable>(key: String, type: T.Type) -> T? {
        get {
            return settings[key]?.value as? T
        }
    }

    func value<T: Codable>(for key: String, type: T.Type, default defaultValue: T) -> T {
        return self[key, type] ?? defaultValue
    }
}

// MARK: - Module-Specific Settings

/// Settings for the Smart Calculator module
struct CalculatorModuleSettings: Codable, Equatable {
    var isEnabled: Bool
    var decimalPlaces: Int
    var angleUnit: AngleUnit
    var enableUnitConversions: Bool
    var enableCurrencyConversions: Bool
    var keyboardShortcut: KeyboardShortcutData?

    enum AngleUnit: String, Codable, CaseIterable {
        case degrees = "degrees"
        case radians = "radians"

        var displayName: String {
            switch self {
            case .degrees: return "Degrees"
            case .radians: return "Radians"
            }
        }
    }

    static let `default` = CalculatorModuleSettings(
        isEnabled: true,
        decimalPlaces: 2,
        angleUnit: .degrees,
        enableUnitConversions: true,
        enableCurrencyConversions: true,
        keyboardShortcut: nil
    )
}

/// Settings for the Clipboard Manager module
struct ClipboardModuleSettings: Codable, Equatable {
    var isEnabled: Bool
    var maxHistoryItems: Int
    var enableImageCapture: Bool
    var enableFileCapture: Bool
    var autoCleanupDays: Int
    var keyboardShortcut: KeyboardShortcutData?

    static let `default` = ClipboardModuleSettings(
        isEnabled: true,
        maxHistoryItems: 100,
        enableImageCapture: true,
        enableFileCapture: true,
        autoCleanupDays: 30,
        keyboardShortcut: nil
    )
}

/// Settings for the Notes Hub module
struct NotesHubModuleSettings: Codable, Equatable {
    var isEnabled: Bool
    var defaultNoteFormat: NoteFormat
    var enableAutoSave: Bool
    var autoSaveInterval: Int // seconds
    var showPreviewInSearch: Bool
    var keyboardShortcut: KeyboardShortcutData?

    enum NoteFormat: String, Codable, CaseIterable {
        case plainText = "plain"
        case markdown = "markdown"

        var displayName: String {
            switch self {
            case .plainText: return "Plain Text"
            case .markdown: return "Markdown"
            }
        }
    }

    static let `default` = NotesHubModuleSettings(
        isEnabled: true,
        defaultNoteFormat: .plainText,
        enableAutoSave: true,
        autoSaveInterval: 30,
        showPreviewInSearch: true,
        keyboardShortcut: nil
    )
}

/// Settings for the Quick Launcher module
struct QuickLauncherModuleSettings: Codable, Equatable {
    var isEnabled: Bool
    var indexSystemApps: Bool
    var indexUserApps: Bool
    var enableFuzzySearch: Bool
    var maxRecentItems: Int
    var keyboardShortcut: KeyboardShortcutData?

    static let `default` = QuickLauncherModuleSettings(
        isEnabled: true,
        indexSystemApps: true,
        indexUserApps: true,
        enableFuzzySearch: true,
        maxRecentItems: 20,
        keyboardShortcut: nil
    )
}

/// Security and privacy settings
struct SecuritySettings: Codable, Equatable {
    var enableDataEncryption: Bool
    var enableClipboardEncryption: Bool
    var enableNotesEncryption: Bool
    var dataRetentionDays: Int
    var autoLockAfterMinutes: Int
    var requireAuthenticationOnLaunch: Bool
    var enableSecureErase: Bool
    var allowDataExport: Bool
    var enableAuditLogging: Bool

    static let `default` = SecuritySettings(
        enableDataEncryption: true,
        enableClipboardEncryption: true,
        enableNotesEncryption: true,
        dataRetentionDays: 365,
        autoLockAfterMinutes: 0, // 0 = disabled
        requireAuthenticationOnLaunch: false,
        enableSecureErase: true,
        allowDataExport: true,
        enableAuditLogging: false
    )
}

/// Type-erased codable wrapper for heterogeneous settings
struct AnyCodable: Codable {
    let value: Any
    
    init(_ value: Any) {
        self.value = value
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        
        if let bool = try? container.decode(Bool.self) {
            value = bool
        } else if let int = try? container.decode(Int.self) {
            value = int
        } else if let double = try? container.decode(Double.self) {
            value = double
        } else if let string = try? container.decode(String.self) {
            value = string
        } else if let array = try? container.decode([AnyCodable].self) {
            value = array.map { $0.value }
        } else if let dictionary = try? container.decode([String: AnyCodable].self) {
            value = dictionary.mapValues { $0.value }
        } else {
            throw DecodingError.dataCorruptedError(in: container, debugDescription: "Unsupported type")
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        
        switch value {
        case let bool as Bool:
            try container.encode(bool)
        case let int as Int:
            try container.encode(int)
        case let double as Double:
            try container.encode(double)
        case let string as String:
            try container.encode(string)
        case let array as [Any]:
            try container.encode(array.map { AnyCodable($0) })
        case let dictionary as [String: Any]:
            try container.encode(dictionary.mapValues { AnyCodable($0) })
        default:
            throw EncodingError.invalidValue(value, EncodingError.Context(codingPath: [], debugDescription: "Unsupported type"))
        }
    }
}
